/* تخصيص عرض الـ carousel */

/* تعديل حاوية الـ carousel */
.carousel-section {
    margin-bottom: 3rem;
    position: relative;
    padding: 0;
    overflow: hidden;
    display: flex;
    justify-content: center; /* توسيط الـ carousel */
}

/* تحديد عرض الـ carousel */
.carousel-container {
    max-width: 900px !important; /* تحديد العرض الأقصى */
    width: 100%;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

/* تعديل عرض الـ carousel-inner */
.carousel-inner {
    max-width: 900px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}

/* تعديل عرض الـ carousel-item */
.carousel-item {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
}

/* تعديل الصور داخل الـ carousel */
.carousel-inner img,
.carousel-img {
    max-width: 900px;
    width: 100%;
    height: 500px;
    object-fit: cover;
    margin: 0 auto;
}

/* تعديل أزرار التنقل */
.carousel-control-prev {
    left: calc(50% - 450px - 25px); /* وضع الزر على يسار الـ carousel */
}

.carousel-control-next {
    right: calc(50% - 450px - 25px); /* وضع الزر على يمين الـ carousel */
}

/* تعديلات للشاشات المتوسطة */
@media (max-width: 992px) {
    .carousel-container,
    .carousel-inner,
    .carousel-item,
    .carousel-inner img,
    .carousel-img {
        max-width: 800px;
    }
    
    .carousel-control-prev {
        left: calc(50% - 400px - 25px);
    }
    
    .carousel-control-next {
        right: calc(50% - 400px - 25px);
    }
}

/* تعديلات للشاشات الصغيرة */
@media (max-width: 768px) {
    .carousel-container,
    .carousel-inner,
    .carousel-item,
    .carousel-inner img,
    .carousel-img {
        max-width: 90%;
    }
    
    .carousel-control-prev {
        left: 10px;
    }
    
    .carousel-control-next {
        right: 10px;
    }
}
