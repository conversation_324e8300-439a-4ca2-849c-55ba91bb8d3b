/* تنسيقات الهيدر في وضع الهاتف */

/* إخفاء الهيدر في وضع الهاتف على الشاشات الكبيرة */
.mobile-header {
    display: none;
}

/* إخفاء النافذة الجانبية على جميع الشاشات بشكل افتراضي */
.mobile-sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
}

.mobile-sidebar-panel {
    position: fixed;
    top: 0;
    left: -300px;
    width: 280px;
    height: 100%;
    background-color: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    transition: left 0.3s ease;
    overflow-y: auto;
    z-index: 2001;
}

.mobile-sidebar-panel.active {
    left: 0;
}

/* عرض الهيدر في وضع الهاتف على الشاشات الصغيرة فقط */
@media (max-width: 767.98px) {
    /* إظهار الهيدر في وضع الهاتف */
    .mobile-header {
        display: block;
        background-color: #fff;
        padding: 10px 0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1000;
    }

    /* إخفاء الهيدر العادي */
    .top-header {
        display: none;
    }

    /* تنسيق حاوية الهيدر في وضع الهاتف */
    .mobile-header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* تنسيق زر السلة */
    .mobile-header-cart {
        order: 1;
    }

    /* تنسيق الشعار */
    .mobile-header-logo {
        order: 2;
        text-align: center;
    }

    /* تنسيق زر القائمة */
    .mobile-header-menu {
        order: 3;
    }

    /* تنسيق صورة الشعار */
    .mobile-logo-img {
        height: 40px;
    }

    /* تنسيق زر القائمة */
    .mobile-menu-button {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #333;
        cursor: pointer;
        padding: 0;
    }

    /* تنسيق رأس النافذة الجانبية */
    .mobile-sidebar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #eee;
        position: relative;
    }

    /* تنسيق العنوان الرئيسي للنافذة الجانبية */
    .mobile-sidebar-title-main {
        position: absolute;
        right: 0;
        left: 0;
        text-align: center;
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
    }

    /* تنسيق زر إغلاق النافذة الجانبية */
    .mobile-sidebar-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        color: #333;
        cursor: pointer;
        padding: 0;
        z-index: 1;
        position: relative;
    }

    /* تنسيق محتوى النافذة الجانبية */
    .mobile-sidebar-content {
        padding: 0;
    }

    /* تنسيق قسم النافذة الجانبية */
    .mobile-sidebar-section {
        margin-bottom: 20px;
    }

    /* تنسيق عنوان القسم */
    .mobile-sidebar-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 10px;
        padding: 10px 15px;
        color: #6a0dad;
    }

    /* تنسيق قائمة الروابط */
    .mobile-sidebar-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    /* تنسيق عنصر القائمة */
    .mobile-sidebar-item {
        margin-bottom: 0;
    }

    /* تنسيق الرابط */
    .mobile-sidebar-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333;
        text-decoration: none;
        padding: 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
        border-bottom: 1px solid #f0f0f0;
    }

    /* تنسيق الجزء الأيمن من الرابط (الأيقونة والنص) */
    .mobile-sidebar-link i {
        margin-left: 15px;
        font-size: 1.3rem;
        min-width: 24px;
        text-align: center;
    }

    /* تنسيق النص في الرابط */
    .mobile-sidebar-link span {
        flex: 1;
    }

    /* تنسيق الرابط عند التحويم */
    .mobile-sidebar-link:hover {
        color: #6a0dad;
        background-color: rgba(106, 13, 173, 0.05);
    }

    /* تنسيق الرابط النشط */
    .mobile-sidebar-link.active {
        color: #6a0dad;
        font-weight: 600;
        background-color: rgba(106, 13, 173, 0.1);
    }

    /* تنسيق خاص للروابط الرئيسية */
    .main-links .mobile-sidebar-link,
    .products-categories-list .mobile-sidebar-link {
        padding: 18px 15px;
    }

    /* تنسيق زر تسجيل الخروج */
    .logout-button {
        width: 100%;
        text-align: right;
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
    }
}
