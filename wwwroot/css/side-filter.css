/* تنسيقات النافذة الجانبية للتصفية والمنتجات الأكثر مبيعاً */

/* زر فتح النافذة الجانبية */
.filter-button {
    position: fixed;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-left: none;
    border-radius: 0 4px 4px 0;
    padding: 10px;
    cursor: pointer;
    z-index: 999;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    display: none; /* إخفاء الزر افتراضياً */
}

.filter-button i {
    font-size: 1.2rem;
    color: #6a0dad;
}

/* إظهار الزر فقط في وضع الهاتف */
@media (max-width: 767px) {
    .filter-button {
        display: block;
    }
}

/* النافذة الجانبية */
.side-filter-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.side-filter-panel {
    position: fixed;
    top: 0;
    left: -350px;
    width: 320px;
    height: 100%;
    background-color: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    overflow-y: auto;
    transition: left 0.3s ease;
}

.side-filter-panel.active {
    left: 0;
}

/* رأس النافذة الجانبية */
.side-filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.side-filter-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.side-filter-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #333;
    padding: 0;
}

/* محتوى النافذة الجانبية */
.side-filter-content {
    padding: 15px;
}

/* قسم تصفية السعر */
.price-filter-container {
    margin-bottom: 20px;
}

.price-filter-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.price-range-slider {
    width: 100%;
    margin-bottom: 15px;
}

.price-inputs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.price-input {
    width: 45%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.price-filter-button {
    width: 100%;
    padding: 10px;
    background-color: #ffc0cb;
    color: #333;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.price-filter-button:hover {
    background-color: #ffb6c1;
}

/* قسم المنتجات الأكثر مبيعاً */
.top-rated-container {
    margin-top: 30px;
}

.top-rated-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.top-rated-product {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.top-rated-product:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.top-rated-img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    margin-left: 10px;
}

.top-rated-info {
    flex: 1;
}

.top-rated-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #333;
}

.top-rated-price {
    font-size: 0.9rem;
    font-weight: 600;
    color: #e83e8c;
}

.top-rated-price .currency {
    font-size: 0.8rem;
    margin-right: 2px;
}

.top-rated-price-original {
    font-size: 0.8rem;
    color: #999;
    text-decoration: line-through;
    margin-left: 5px;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 767px) {
    .side-filter-panel {
        width: 280px;
    }
}
