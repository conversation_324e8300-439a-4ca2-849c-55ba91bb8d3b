/* Estilos para la visualización de productos según el nuevo diseño */

/* Contenedor de la cuadrícula de productos */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 20px;
}

/* Tarjeta de producto */
.product-item {
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
    margin-bottom: 20px;
}

.product-item:hover {
    transform: translateY(-5px);
}

/* Contenedor de imagen */
.product-item-img-container {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 0;
    padding-bottom: 100%; /* Relación de aspecto cuadrada */
}

.product-item-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-item:hover .product-item-img {
    transform: scale(1.05);
}

/* أزرار الإجراءات التي تظهر عند تمرير المؤشر */
.product-item-actions {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 20;
    pointer-events: auto;
}

/* إصلاح مشكلة الرابط الممتد */
.product-link {
    z-index: 5;
    pointer-events: auto;
}

/* تعديل الرابط الممتد لتجنب تداخله مع الأزرار */
.product-item .stretched-link::after {
    z-index: 10;
    pointer-events: none;
}

.product-item:hover .product-item-actions {
    opacity: 1;
    transform: translateY(0);
}

.product-item-action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: white;
    color: #333;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.product-item-action-btn:hover {
    background-color: #6a0dad;
    color: white;
    transform: translateY(-3px);
}

.product-item-action-btn.active {
    background-color: #6a0dad;
    color: white;
}

/* Etiqueta de descuento - دائرة وردية في أعلى اليمين */
.product-item-discount {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #ffd1dc; /* لون وردي أفتح */
    color: #666; /* لون نص أفتح */
    padding: 6px; /* تقليل المسافة الداخلية */
    border-radius: 50%; /* دائرة */
    font-size: 12px; /* تقليل حجم الخط */
    font-weight: 500; /* وزن خط أقل */
    z-index: 2;
    width: 35px; /* تقليل العرض */
    height: 35px; /* تقليل الارتفاع */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* ظل أخف */
    transition: transform 0.3s ease;
}

/* تأثير حركي عند تمرير المؤشر */
.product-item:hover .product-item-discount {
    transform: scale(1.1);
}

/* Etiqueta de disponibilidad - نقل إلى اليسار */
.product-item-availability {
    position: absolute;
    top: 10px;
    left: 10px; /* تغيير من right إلى left */
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 2;
}

.product-item-availability.available {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

.product-item-availability.not-available {
    background-color: rgba(255, 0, 0, 0.1);
    color: #ff0000;
}

/* Información del producto */
.product-item-info {
    padding: 10px 0;
    text-align: center;
}

.product-item-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
    text-align: center;
}

.product-item-price {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.product-item-price-current {
    font-size: 16px;
    font-weight: bold;
    color: #6a0dad;
}

.product-item-price-original {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

/* Título de sección */
.section-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
    color: #333;
    position: relative;
    padding-bottom: 10px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: #6a0dad;
}

/* Responsive */
@media (max-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .products-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}
