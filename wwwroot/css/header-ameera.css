/* تنسيق رأس الصفحة على طراز Ameera */

/* الشريط العلوي */
.top-header {
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

/* تنسيق الشعار */
.navbar-brand img {
    max-height: 60px;
}

/* تنسيق قسم الاتصال */
.contact-info {
    text-align: right;
}

.contact-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.contact-email, .contact-phone {
    color: #666;
    font-size: 0.9rem;
    direction: ltr;
    display: inline-block;
}

/* تنسيق أيقونات الجزء الأيسر */
.left-section {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.header-icons-container {
    display: flex;
     
    align-items: center;
    gap: 5px; /* تقليل المباعدة بين العناصر بشكل أكبر */
    width: 100%;
}

.icon-link {
    color: #333;
    font-size: 1.2rem;
    position: relative;
    text-decoration: none;
    margin: 0 5px; /* تقليل المباعدة بين الأيقونات */
}

.icon-link:hover {
    color: #6a0dad;
}

/* تنسيق سلة التسوق */
.cart-container, .cart-price-container {
    display: flex;
    align-items: center;
    gap: 5px;
}

.cart-price {
    margin-left: 0;
    font-size: 0.9rem;
    color: #333;
    direction: ltr;
    display: inline-block;
}

/* تنسيق روابط تسجيل الدخول */
.login-register {
    margin-left: 15px;
}

.login-register a {
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
}

.login-register a:hover {
    color: #6a0dad;
}

.login-register span {
    margin: 0 5px;
    color: #999;
}

/* تنسيق شريط التنقل الرئيسي */
.main-navigation {
    background-color: #fff;
    border-bottom: 1px solid #eee;
    padding: 0;
}

.main-navigation .navbar-nav {
    width: 100%;
    display: flex;
    justify-content: center;
}

.main-navigation .nav-item {
    margin: 0 8px; /* تقليل المباعدة بين عناصر القائمة */
}

.main-navigation .nav-link {
    color: #333;
    font-weight: 500;
    padding: 15px 0;
    position: relative;
}

.main-navigation .nav-link:hover,
.main-navigation .nav-link.active {
    color: #6a0dad;
}

.main-navigation .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #6a0dad;
    transition: width 0.3s ease;
}

.main-navigation .nav-link:hover::after,
.main-navigation .nav-link.active::after {
    width: 100%;
}

/* تنسيق العدادات */
.badge {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 0.7rem;
    background-color: #6a0dad;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تنسيق القائمة المنسدلة */
.dropdown-menu {
    border-radius: 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: none;
    padding: 10px 0;
}

.dropdown-item {
    padding: 8px 20px;
    color: #333;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 992px) {
    .top-header .row {
        flex-direction: column;
    }

    .top-header .col-md-4 {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }

    .left-section {
        justify-content: center;
    }

    .contact-info {
        text-align: center;
    }

    .main-navigation .navbar-nav {
        margin-top: 10px;
    }
}
