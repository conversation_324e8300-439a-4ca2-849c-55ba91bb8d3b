// كود JavaScript للنافذة الجانبية للتصفية والمنتجات الأكثر مبيعاً

document.addEventListener('DOMContentLoaded', function () {
    // تهيئة النافذة الجانبية
    initSideFilter();
});

function initSideFilter() {
    // التحقق مما إذا كان الجهاز هاتفًا محمولًا
    if (isMobileDevice()) {
        // إضافة زر فتح النافذة الجانبية إلى الصفحة
        addFilterButton();

        // إضافة مستمعات الأحداث
        const filterButton = document.querySelector('.filter-button');
        const closeButton = document.querySelector('.side-filter-close');
        const overlay = document.querySelector('.side-filter-overlay');
        const panel = document.querySelector('.side-filter-panel');

        if (filterButton) {
            filterButton.addEventListener('click', function() {
                openSideFilter();
            });
        }

        if (closeButton) {
            closeButton.addEventListener('click', function() {
                closeSideFilter();
            });
        }

        if (overlay) {
            overlay.addEventListener('click', function() {
                closeSideFilter();
            });
        }

        // تهيئة شريط تمرير السعر
        initPriceSlider();
    }
}

// التحقق مما إذا كان الجهاز هاتفًا محمولًا
function isMobileDevice() {
    return (window.innerWidth <= 767) ||
           (navigator.userAgent.match(/Android/i) ||
            navigator.userAgent.match(/webOS/i) ||
            navigator.userAgent.match(/iPhone/i) ||
            navigator.userAgent.match(/iPad/i) ||
            navigator.userAgent.match(/iPod/i) ||
            navigator.userAgent.match(/BlackBerry/i) ||
            navigator.userAgent.match(/Windows Phone/i));
}

function addFilterButton() {
    // التحقق من وجود الزر بالفعل
    if (document.querySelector('.filter-button')) {
        return;
    }

    // إنشاء زر فتح النافذة الجانبية
    const filterButton = document.createElement('button');
    filterButton.className = 'filter-button';
    filterButton.innerHTML = '<i class="bi bi-funnel-fill"></i>';
    filterButton.setAttribute('title', 'تصفية المنتجات');

    // إنشاء النافذة الجانبية
    const sideFilterHTML = `
        <div class="side-filter-overlay"></div>
        <div class="side-filter-panel">
            <div class="side-filter-header">
                <h3 class="side-filter-title">تصفية المنتجات</h3>
                <button class="side-filter-close">&times;</button>
            </div>
            <div class="side-filter-content">
                <!-- قسم تصفية السعر -->
                <div class="price-filter-container">
                    <h3 class="price-filter-title">تصفية حسب السعر</h3>
                    <input type="range" class="price-range-slider" min="0" max="20" value="20" id="priceRangeSlider">
                    <div class="price-inputs">
                        <input type="text" class="price-input" value="0 ر.ع" id="minPrice" readonly>
                        <input type="text" class="price-input" value="20 ر.ع" id="maxPrice" readonly>
                    </div>
                    <button class="price-filter-button" id="applyPriceFilter">تصفية</button>
                </div>

                <!-- قسم المنتجات الأكثر مبيعاً -->
                <div class="top-rated-container">
                    <h3 class="top-rated-title">المنتجات الأكثر مبيعاً</h3>
                    <div id="topRatedProductsContainer">
                        <!-- سيتم تحميل المنتجات الأكثر مبيعاً هنا -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة العناصر إلى الصفحة
    document.body.appendChild(filterButton);
    document.body.insertAdjacentHTML('beforeend', sideFilterHTML);

    // تحميل المنتجات الأكثر مبيعاً
    loadTopRatedProducts();
}

function openSideFilter() {
    const overlay = document.querySelector('.side-filter-overlay');
    const panel = document.querySelector('.side-filter-panel');

    if (overlay && panel) {
        overlay.style.display = 'block';
        panel.classList.add('active');
        document.body.style.overflow = 'hidden'; // منع التمرير في الصفحة الرئيسية
    }
}

function closeSideFilter() {
    const overlay = document.querySelector('.side-filter-overlay');
    const panel = document.querySelector('.side-filter-panel');

    if (overlay && panel) {
        overlay.style.display = 'none';
        panel.classList.remove('active');
        document.body.style.overflow = ''; // السماح بالتمرير مرة أخرى
    }
}

function initPriceSlider() {
    const slider = document.getElementById('priceRangeSlider');
    const minPrice = document.getElementById('minPrice');
    const maxPrice = document.getElementById('maxPrice');
    const applyButton = document.getElementById('applyPriceFilter');

    if (slider && maxPrice) {
        slider.addEventListener('input', function() {
            maxPrice.value = this.value + ' ر.ع';
        });
    }

    if (applyButton) {
        applyButton.addEventListener('click', function() {
            filterProductsByPrice();
            closeSideFilter();
        });
    }
}

function filterProductsByPrice() {
    const maxPriceValue = parseInt(document.getElementById('priceRangeSlider').value);
    const productItems = document.querySelectorAll('.product-item');
    let visibleCount = 0;
    let totalCount = productItems.length;

    productItems.forEach(item => {
        const priceElement = item.querySelector('.product-item-price-current');
        if (priceElement) {
            // استخراج قيمة السعر من النص
            const priceText = priceElement.textContent;
            const priceValue = parseFloat(priceText.replace(/[^\d.]/g, ''));

            if (priceValue <= maxPriceValue) {
                item.style.display = '';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        }
    });

    // تحديث عداد المنتجات المعروضة
    const productsCountElement = document.querySelector('.products-count span:first-child');
    if (productsCountElement) {
        productsCountElement.textContent = visibleCount;
    }

    // عرض رسالة للمستخدم
    showToast(`تم تطبيق التصفية: ${visibleCount} من أصل ${totalCount} منتج`);
}

function loadTopRatedProducts() {
    const container = document.getElementById('topRatedProductsContainer');

    if (!container) {
        return;
    }

    // محاولة الحصول على المنتجات الأكثر مبيعاً من الصفحة الحالية
    // نقوم بالبحث عن المنتجات في الصفحة الرئيسية
    const productItems = document.querySelectorAll('.product-item');

    if (productItems && productItems.length > 0) {
        // أخذ أول 4 منتجات من الصفحة (يمكن تعديل المنطق لاختيار المنتجات الأكثر مبيعاً)
        const topProducts = Array.from(productItems)
            .sort((a, b) => {
                const priceA = getPriceFromProductItem(a);
                const priceB = getPriceFromProductItem(b);
                return priceB - priceA; // ترتيب تنازلي حسب السعر
            })
            .slice(0, 4);

        // إنشاء عناصر المنتجات الأكثر مبيعاً
        topProducts.forEach(product => {
            const productName = product.querySelector('.product-item-title')?.textContent || 'منتج';
            const productPrice = product.querySelector('.product-item-price-current')?.textContent || '0 ر.ع';
            const productImage = product.querySelector('.product-item-img')?.getAttribute('src') || '/images/placeholder.jpg';
            const productId = product.querySelector('[data-product-id]')?.getAttribute('data-product-id') || '';

            const topRatedProduct = document.createElement('div');
            topRatedProduct.className = 'top-rated-product';
            topRatedProduct.innerHTML = `
                <img src="${productImage}" class="top-rated-img" alt="${productName}">
                <div class="top-rated-info">
                    <h4 class="top-rated-name">${productName}</h4>
                    <div class="top-rated-price">${productPrice}</div>
                </div>
            `;

            // إضافة رابط للمنتج
            topRatedProduct.style.cursor = 'pointer';
            topRatedProduct.addEventListener('click', function() {
                window.location.href = `/Products/Details/${productId}`;
            });

            container.appendChild(topRatedProduct);
        });
    } else {
        // إذا لم تكن المنتجات موجودة، نعرض رسالة
        container.innerHTML = `
            <div class="text-center py-3">
                <p>لا توجد منتجات متاحة حالياً</p>
            </div>
        `;
    }
}

// وظيفة مساعدة لاستخراج السعر من عنصر المنتج
function getPriceFromProductItem(item) {
    const priceElement = item.querySelector('.product-item-price-current');
    if (priceElement) {
        const priceText = priceElement.textContent;
        // استخراج الأرقام فقط من النص
        const priceValue = parseFloat(priceText.replace(/[^\d.]/g, ''));
        return priceValue;
    }
    return 0;
}

// وظيفة مساعدة لعرض رسالة توست
function showToast(message) {
    // التحقق من وجود عنصر التوست
    let toastContainer = document.querySelector('.toast-container');

    if (!toastContainer) {
        // إنشاء حاوية التوست إذا لم تكن موجودة
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // إنشاء عنصر التوست
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;

    // إضافة التوست إلى الحاوية
    toastContainer.appendChild(toast);

    // إظهار التوست
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // إخفاء التوست بعد 3 ثوان
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}
