/* تنسيق نافذة تسجيل الدخول الجانبية */

.side-login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.side-login-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 400px;
    height: 100%;
    background-color: white;
    z-index: 1001;
    overflow-y: auto;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.side-login-panel.active {
    transform: translateX(0);
}

.side-login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.side-login-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.side-login-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.side-login-close:hover {
    color: #333;
}

.side-login-body {
    padding: 20px;
}

.side-login-form .form-group {
    margin-bottom: 20px;
}

.side-login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    text-align: right;
}

.side-login-form label .required {
    color: #dc3545;
    margin-right: 4px;
}

.side-login-form input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.side-login-form input:focus {
    border-color: #6a0dad;
    outline: none;
}

.side-login-form .password-field {
    position: relative;
}

.side-login-form .password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
}

.side-login-form .submit-btn {
    width: 100%;
    padding: 12px;
    background-color: #a94442;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.side-login-form .submit-btn:hover {
    background-color: #953b39;
}

.side-login-form .form-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
}

.side-login-form .remember-me {
    display: flex;
    align-items: center;
}

.side-login-form .remember-me input {
    width: auto;
    margin-left: 8px;
}

.side-login-form .forgot-password {
    color: #a94442;
    text-decoration: none;
    font-size: 0.9rem;
}

.side-login-form .forgot-password:hover {
    text-decoration: underline;
}

.side-login-divider {
    margin: 30px 0;
    text-align: center;
    position: relative;
}

.side-login-divider::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #eee;
}

.side-login-register {
    text-align: center;
    margin-top: 30px;
}

.side-login-register .register-icon {
    width: 60px;
    height: 60px;
    background-color: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.side-login-register .register-icon i {
    font-size: 2rem;
    color: #ccc;
}

.side-login-register p {
    margin-bottom: 15px;
    color: #666;
}

.side-login-register a {
    color: #a94442;
    text-decoration: none;
    font-weight: 500;
}

.side-login-register a:hover {
    text-decoration: underline;
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 576px) {
    .side-login-panel {
        width: 100%;
    }
}
