/* تنسيق مخصص لشريط التنقل */

/* تنسيق زر جميع المنتجات */
.all-products-btn {
    background-color: #6a0dad; /* لون خلفية بنفسجي */
    color: white !important; /* لون النص أبيض */
    border-radius: 20px; /* حواف دائرية */
    padding: 8px 20px !important; /* تباعد داخلي */
    margin: 0 5px; /* تباعد خارجي */
    transition: all 0.3s ease; /* تأثير انتقالي */
}

.all-products-btn:hover {
    background-color: #5a0b9d; /* لون خلفية بنفسجي داكن عند التحويم */
    color: white !important; /* لون النص أبيض */
}

/* تنسيق روابط القائمة الرئيسية */
.main-nav .nav-link {
    color: #333; /* لون النص أسود */
    font-weight: 500; /* سمك الخط */
    padding: 8px 15px; /* تباعد داخلي */
    transition: all 0.3s ease; /* تأثير انتقالي */
    position: relative; /* للتمكن من إضافة خط تحت الرابط */
    margin: 0 5px; /* تباعد بين الروابط */
}

.main-nav .nav-link:hover {
    color: #6a0dad; /* لون النص بنفسجي عند التحويم */
}

.main-nav .nav-link:hover::after {
    content: '';
    position: absolute;
    width: 50%;
    height: 2px;
    background-color: #6a0dad;
    bottom: 0;
    left: 25%;
    transition: all 0.3s ease;
}

/* تنسيق القائمة الرئيسية */
.main-nav {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* محاذاة العناصر إلى اليمين (في RTL تكون اليمين) */
    flex-grow: 1;
    padding-right: 20px; /* تباعد إضافي من اليمين */
}

/* تنسيق حاوية القائمة */
.navbar-collapse {
    justify-content: flex-end; /* محاذاة القائمة إلى اليسار */
}

/* تنسيق عنصر القائمة */
.main-nav .nav-item {
    margin: 0 5px; /* تباعد خارجي */
}

/* تنسيق الروابط النشطة */
.main-nav .nav-link.active {
    color: #6a0dad !important; /* لون النص بنفسجي للروابط النشطة */
    font-weight: 600; /* سمك الخط للروابط النشطة */
    border-bottom: 2px solid #6a0dad; /* إضافة خط تحت الرابط النشط */
    padding-bottom: 5px; /* تباعد إضافي للخط */
    background-color: rgba(106, 13, 173, 0.1); /* خلفية شفافة بنفسجية */
    border-radius: 5px; /* حواف دائرية */
}

/* تنسيق إضافي للروابط النشطة في صفحات الفئات */
.main-nav .nav-link[href*="category"] {
    position: relative;
}

/* تنسيق خاص للرابط النشط في صفحة العبايات */
.main-nav .nav-link[href*="category=abayat"].active,
.main-nav .nav-link[href*="category=عبايات"].active {
    color: #6a0dad !important;
    font-weight: 600;
    border-bottom: 2px solid #6a0dad;
    background-color: rgba(106, 13, 173, 0.1);
}
