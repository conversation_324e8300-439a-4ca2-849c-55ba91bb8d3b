/* تنسيق روابط تسجيل الدخول والتسجيل */

/* حاوية روابط التسجيل */
.login-register {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    margin-right: 10px;
}

/* تنسيق أيقونة المستخدم عند تسجيل الدخول */
.login-register .dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 0;
    border-radius: 50%;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.login-register .dropdown-toggle:hover,
.login-register .dropdown-toggle:focus {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* إخفاء النص والسهم */
.login-register .user-text {
    display: none;
}

/* تنسيق أيقونة السهم للأسفل */
.login-register .dropdown-toggle::after {
    display: none;
}

/* تنسيق أيقونة المستخدم */
.login-register .user-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #6a0dad;
    color: white;
    margin-right: 0;
}

.login-register .user-icon i {
    font-size: 1.2rem;
}

/* تنسيق القائمة المنسدلة */
.login-register .dropdown-menu {
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: none;
    padding: 12px;
    min-width: 220px;
    margin-top: 10px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-register .dropdown-item {
    border-radius: 10px;
    padding: 10px 15px;
    margin-bottom: 5px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.login-register .dropdown-item:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(-3px);
}

.login-register .dropdown-item i {
    margin-left: 10px;
    color: #6a0dad;
    font-size: 1.1rem;
}

/* تنسيق زر تسجيل الخروج */
.login-register .dropdown-item[type="submit"] {
    background: none;
    border: none;
    width: 100%;
    text-align: right;
    cursor: pointer;
}

/* تنسيق الفاصل في القائمة المنسدلة */
.login-register .dropdown-divider {
    margin: 8px 0;
    border-top: 1px solid rgba(106, 13, 173, 0.1);
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 768px) {
    .login-register .dropdown-toggle {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    .login-register .dropdown-toggle i {
        font-size: 1rem;
    }

    .login-register .dropdown-menu {
        min-width: 200px;
        padding: 10px;
    }

    .login-register .dropdown-item {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
}
