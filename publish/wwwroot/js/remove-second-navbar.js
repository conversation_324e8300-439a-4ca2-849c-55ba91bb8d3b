// إزالة الشريط الثاني عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على جميع أشرطة التنقل في الصفحة
    const navbars = document.querySelectorAll('.navbar');
    
    // إذا كان هناك أكثر من شريط تنقل واحد
    if (navbars.length > 1) {
        // الحصول على الشريط الرئيسي
        const mainNavbar = document.querySelector('.main-navigation');
        
        // إزالة جميع أشرطة التنقل الأخرى
        navbars.forEach(navbar => {
            // تجاهل الشريط الرئيسي
            if (navbar !== mainNavbar && !navbar.classList.contains('main-navigation')) {
                navbar.remove();
            }
        });
    }
    
    // إزالة أي شريط تنقل يظهر بعد العنصر header مباشرة
    const header = document.querySelector('header');
    if (header && header.nextElementSibling) {
        const nextElement = header.nextElementSibling;
        if (nextElement.tagName === 'NAV' || nextElement.classList.contains('navbar')) {
            nextElement.remove();
        }
    }
    
    // إزالة أي شريط تنقل يحتوي على روابط الفئات
    const allNavs = document.querySelectorAll('nav');
    allNavs.forEach(nav => {
        if (nav.textContent.includes('جميع المنتجات') || 
            nav.textContent.includes('المخور') || 
            nav.textContent.includes('الإكسسوارات') || 
            nav.textContent.includes('بخور') || 
            nav.textContent.includes('الأقمشة') || 
            nav.textContent.includes('عبايات')) {
            
            // تجاهل الشريط الرئيسي
            if (!nav.classList.contains('main-navigation')) {
                nav.remove();
            }
        }
    });
});
