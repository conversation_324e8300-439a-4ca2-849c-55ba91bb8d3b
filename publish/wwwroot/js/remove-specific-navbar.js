// إزالة الشريط السفلي المحدد
document.addEventListener('DOMContentLoaded', function() {
    // استهداف الشريط السفلي بناءً على الصورة
    // البحث عن جميع أشرطة التنقل التي تحتوي على "جميع المنتجات"
    const allNavbars = document.querySelectorAll('.navbar, nav');
    
    // تحديد الشريط الثاني (الشريط السفلي) وإزالته
    let mainNavFound = false;
    
    allNavbars.forEach(navbar => {
        // إذا كان هذا هو الشريط الرئيسي، نتجاهله
        if (navbar.classList.contains('main-navigation')) {
            mainNavFound = true;
            return;
        }
        
        // إذا وجدنا الشريط الرئيسي بالفعل، فهذا هو الشريط الثاني
        if (mainNavFound) {
            navbar.remove();
            return;
        }
        
        // إذا كان الشريط يحتوي على زر "جميع المنتجات" البنفسجي
        if (navbar.querySelector('.btn-primary, .btn-purple, .bg-purple') || 
            navbar.classList.contains('bg-purple') || 
            navbar.classList.contains('bg-primary')) {
            navbar.remove();
            return;
        }
        
        // إذا كان الشريط يحتوي على روابط الفئات
        if (navbar.textContent.includes('جميع المنتجات') && 
            (navbar.textContent.includes('المخور') || 
             navbar.textContent.includes('الإكسسوارات') || 
             navbar.textContent.includes('بخور') || 
             navbar.textContent.includes('الأقمشة') || 
             navbar.textContent.includes('عبايات'))) {
            navbar.remove();
            return;
        }
    });
    
    // استهداف محدد: البحث عن الشريط الذي يحتوي على زر "جميع المنتجات" البنفسجي
    const purpleButtons = document.querySelectorAll('.btn-primary, .btn-purple, .bg-purple');
    purpleButtons.forEach(button => {
        if (button.textContent.includes('جميع المنتجات')) {
            // العثور على الشريط الأب وإزالته
            let parent = button.parentElement;
            while (parent && !parent.classList.contains('navbar') && !parent.tagName === 'NAV') {
                parent = parent.parentElement;
            }
            if (parent) {
                parent.remove();
            }
        }
    });
    
    // استهداف محدد: إزالة أي شريط تنقل يظهر بعد الشريط الرئيسي مباشرة
    const mainNav = document.querySelector('.navbar');
    if (mainNav && mainNav.nextElementSibling) {
        const nextElement = mainNav.nextElementSibling;
        if (nextElement.classList.contains('navbar') || nextElement.tagName === 'NAV') {
            nextElement.remove();
        }
    }
});
