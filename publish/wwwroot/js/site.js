// Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
// for details on configuring this project to bundle and minify static web assets.

// Write your JavaScript code.
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded and parsed');

    // تهيئة الشريط المتحرك مع الصور الجانبية
    initCarousel();

    // Add any client-side functionality here

    // تحديث عدد العناصر في السلة بعد إضافة منتج من نموذج
    $(document).on('click', 'form .add-to-cart-btn, form [type="submit"]', function(e) {
        // استثناء نموذج تسجيل الدخول والتسجيل
        // التحقق من معرف النموذج
        if ($(this).closest('form').attr('id') === 'account' ||
            $(this).closest('form').attr('id') === 'registerForm' ||
            $(this).closest('form').attr('id') === 'trackingForm') {
            console.log('تم استثناء نموذج تسجيل الدخول أو التسجيل أو تتبع الطلب');
            return true; // السماح بإرسال نموذج تسجيل الدخول والتسجيل وتتبع الطلب بشكل طبيعي
        }

        // التحقق من معرف الزر
        if ($(this).attr('id') === 'login-submit') {
            console.log('تم استثناء زر تسجيل الدخول');
            return true; // السماح بإرسال نموذج تسجيل الدخول بشكل طبيعي
        }

        // التحقق من عنوان النموذج - استثناء نماذج المدير
        if ($(this).closest('form').attr('action')) {
            const formAction = $(this).closest('form').attr('action');

            // استثناء نماذج المدير
            if (formAction.includes('/Products/Create') ||
                formAction.includes('/Products/Edit') ||
                formAction.includes('/Products/Delete') ||
                formAction.includes('/Categories/Create') ||
                formAction.includes('/Categories/Edit') ||
                formAction.includes('/Categories/Delete') ||
                formAction.includes('/CarouselImages/Create') ||
                formAction.includes('/CarouselImages/Edit') ||
                formAction.includes('/CarouselImages/Delete') ||
                formAction.includes('/Admin') ||
                formAction.includes('/Users')) {
                console.log('تم استثناء نموذج المدير: ' + formAction);
                return true; // السماح بإرسال نماذج المدير بشكل طبيعي
            }

            // استثناء نماذج تسجيل الدخول والتسجيل
            if (formAction.includes('/Identity/Account/Login') ||
                formAction.includes('/Identity/Account/Register')) {
                console.log('تم استثناء نموذج تسجيل الدخول أو التسجيل بناءً على العنوان');
                return true; // السماح بإرسال نموذج تسجيل الدخول والتسجيل بشكل طبيعي
            }

            // استثناء نموذج تتبع الطلب
            if (formAction.includes('/OrderTracking/Track')) {
                console.log('تم استثناء نموذج تتبع الطلب بناءً على العنوان');
                return true; // السماح بإرسال نموذج تتبع الطلب بشكل طبيعي
            }
        }

        // التحقق من وجود كلمة المرور في النموذج
        if ($(this).closest('form').find('input[type="password"]').length > 0) {
            console.log('تم استثناء النموذج لأنه يحتوي على حقل كلمة مرور');
            return true; // السماح بإرسال النموذج بشكل طبيعي
        }

        // التحقق من وجود سمة data-custom-submit
        if ($(this).closest('form').attr('data-custom-submit') === 'true') {
            console.log('تم استثناء النموذج لأنه يحتوي على سمة data-custom-submit');
            return true; // السماح بإرسال النموذج بشكل طبيعي
        }

        // التحقق من وجود سمة enctype="multipart/form-data"
        if ($(this).closest('form').attr('enctype') === 'multipart/form-data') {
            console.log('تم استثناء النموذج لأنه يحتوي على سمة enctype="multipart/form-data"');
            return true; // السماح بإرسال النموذج بشكل طبيعي
        }

        // إذا لم يتم استثناء النموذج، قم بمنع الإرسال الافتراضي
        e.preventDefault();
        const form = $(this).closest('form');

        // التحقق من أن النموذج موجود ويحتوي على عنوان
        if (form.length && form.attr('action')) {
            const formData = form.serialize();

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    if (response.success) {
                        // عرض رسالة نجاح
                        if (typeof showToast === 'function') {
                            showToast('تمت إضافة المنتج إلى السلة');
                        }

                        // تحديث عدد العناصر في السلة
                        $(document).trigger('cart:updated');

                        // تحديث عداد السلة مباشرة إذا كانت الدالة موجودة
                        if (typeof updateCartCount === 'function') {
                            updateCartCount();
                        }
                    } else if (response.message) {
                        // عرض رسالة الخطأ
                        if (typeof showToast === 'function') {
                            showToast(response.message);
                        } else {
                            alert(response.message);
                        }
                    }
                },
                error: function() {
                    // عرض رسالة خطأ
                    if (typeof showToast === 'function') {
                        showToast('حدث خطأ أثناء إضافة المنتج');
                    } else {
                        alert('حدث خطأ أثناء إضافة المنتج');
                    }
                }
            });

            return false; // منع إرسال النموذج بشكل تقليدي
        }
    });

    // إضافة منتج للسلة من خلال زر مستقل (خارج النموذج)
    $(document).on('click', '.product-actions-menu .add-to-cart-btn, .product-action-btn.add-to-cart-btn', function(e) {
        e.preventDefault();

        // التحقق من أن الزر ليس داخل نموذج (تم التعامل معه في الحدث السابق)
        if ($(this).closest('form').length === 0) {
            const productId = $(this).data('product-id');
            const token = $('input[name="__RequestVerificationToken"]').val();

            if (productId) {
                $.ajax({
                    url: '/Cart/AddToCart',
                    type: 'POST',
                    data: {
                        productId: productId,
                        quantity: 1,
                        __RequestVerificationToken: token
                    },
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        if (response.success) {
                            // عرض رسالة نجاح
                            if (typeof showToast === 'function') {
                                showToast('تمت إضافة المنتج إلى السلة');
                            }

                            // تحديث عدد العناصر في السلة
                            $(document).trigger('cart:updated');

                            // تحديث عداد السلة مباشرة إذا كانت الدالة موجودة
                            if (typeof updateCartCount === 'function') {
                                updateCartCount();
                            }
                        } else if (response.message) {
                            // عرض رسالة الخطأ
                            if (typeof showToast === 'function') {
                                showToast(response.message);
                            } else {
                                alert(response.message);
                            }
                        }
                    },
                    error: function() {
                        // عرض رسالة خطأ
                        if (typeof showToast === 'function') {
                            showToast('حدث خطأ أثناء إضافة المنتج');
                        } else {
                            alert('حدث خطأ أثناء إضافة المنتج');
                        }
                    }
                });
            }
        }
    });

    // دالة عامة لتحديث عدد العناصر في السلة
    window.updateCartCount = function() {
        $.ajax({
            url: '/Cart/GetCartItemsCount',
            type: 'GET',
            success: function(response) {
                const cartBadge = document.querySelector('.cart-count');
                if (cartBadge) {
                    cartBadge.textContent = response.count;

                    // تحريك العداد
                    cartBadge.classList.add('pulse');
                    setTimeout(() => {
                        cartBadge.classList.remove('pulse');
                    }, 1000);
                }
            }
        });
    };

    // دالة عامة لعرض رسائل التنبيه
    window.showToast = function(message) {
        // إنشاء عنصر التنبيه إذا لم يكن موجوداً
        let toast = document.getElementById('toast-notification');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'toast-notification';
            // استخدام كلاس CSS بدلاً من تعيين الأنماط مباشرة
            toast.className = 'toast-notification';
            toast.style.opacity = '0';
            document.body.appendChild(toast);
        }

        // عرض الرسالة
        toast.textContent = message;
        toast.style.opacity = '1';

        // إخفاء الرسالة بعد 3 ثوان
        setTimeout(() => {
            toast.style.opacity = '0';
        }, 3000);
    };

    // دالة تهيئة الشريط المتحرك
    function initCarousel() {
        const carousel = document.getElementById('productCarousel');
        if (!carousel) return;

        // تعديل سلوك الشريط المتحرك الافتراضي
        try {
            // تعديل طريقة عرض الشريط المتحرك
            const carouselItems = carousel.querySelectorAll('.carousel-item');
            if (carouselItems.length === 0) return;

            // تعديل إعدادات الشريط المتحرك
            const carouselOptions = {
                interval: 7000,  // زيادة الفاصل الزمني بين الشرائح
                wrap: true,      // السماح بالتكرار
                pause: 'hover',  // إيقاف مؤقت عند التحويم
                keyboard: true,  // السماح بالتنقل باستخدام لوحة المفاتيح
                touch: true      // السماح بالتنقل باللمس
            };

            // تهيئة الشريط المتحرك بالإعدادات
            let carouselInstance;
            try {
                carouselInstance = new bootstrap.Carousel(carousel, carouselOptions);
            } catch (e) {
                console.warn('خطأ في تهيئة الشريط المتحرك:', e);
                return; // الخروج من الدالة إذا فشلت تهيئة الشريط المتحرك
            }

            // إضافة تأثيرات حركية للصور
            addImagesAnimation();

            // إذا كان هناك عنصر واحد فقط، لا داعي لإضافة أحداث التنقل
            if (carouselItems.length <= 1) return;

            // إضافة الأحداث للأزرار
            const prevButton = carousel.querySelector('.carousel-control-prev');
            const nextButton = carousel.querySelector('.carousel-control-next');

            if (prevButton) {
                prevButton.addEventListener('click', function() {
                    // إضافة تأثيرات حركية بعد التغيير
                    setTimeout(() => {
                        addSlideAnimation('prev');
                    }, 100);
                });
            }

            if (nextButton) {
                nextButton.addEventListener('click', function() {
                    // إضافة تأثيرات حركية بعد التغيير
                    setTimeout(() => {
                        addSlideAnimation('next');
                    }, 100);
                });
            }

            // تم تعطيل تحديث الصور الجانبية عند تحميل الصفحة لمنع تداخل الصور

            // إضافة حدث للمؤشرات
            const indicators = carousel.querySelectorAll('.carousel-indicators button');
            if (indicators.length > 0) {
                indicators.forEach(indicator => {
                    indicator.addEventListener('click', function() {
                        // إضافة تأثيرات حركية بعد التغيير
                        setTimeout(() => {
                            addSlideAnimation('next');
                        }, 100);
                    });
                });
            }

            // إضافة حدث عند تغيير الشريحة
            carousel.addEventListener('slide.bs.carousel', function(e) {
                const direction = e.direction === 'right' ? 'prev' : 'next';
                addSlideAnimation(direction);
            });

            // دالة لإضافة تأثيرات حركية للصور
            function addImagesAnimation() {
                // إضافة تأثيرات حركية للصور النشطة
                const activeItems = carousel.querySelectorAll('.carousel-item');
                activeItems.forEach(item => {
                    const img = item.querySelector('img');
                    if (img) {
                        img.style.transition = 'transform 1.2s ease, filter 0.8s ease';
                    }
                });
            }

            // دالة لإضافة تأثيرات حركية إضافية
            function addSlideAnimation(direction) {
                const activeItem = carousel.querySelector('.carousel-item.active');
                if (!activeItem) return;

                // إضافة تأثير حركي للصورة النشطة
                const activeImage = activeItem.querySelector('img');
                if (activeImage) {
                    activeImage.style.animation = 'none';
                    setTimeout(() => {
                        activeImage.style.animation = 'subtle-zoom 15s infinite alternate';
                    }, 50);
                }

                // إضافة تأثير حركي للوصف
                const caption = activeItem.querySelector('.carousel-caption');
                if (caption) {
                    caption.style.animation = 'none';
                    setTimeout(() => {
                        caption.style.animation = 'slideUp 0.8s forwards';
                    }, 50);
                }
            }

            // دالة لتحديث الصور الجانبية - تم تعطيلها لمنع تداخل الصور
            function updateSideImages() {
                // تم تعطيل هذه الدالة لمنع تداخل الصور
                return;
            }
        } catch (error) {
            console.error('خطأ في تهيئة الشريط المتحرك:', error);
        }
    }
});