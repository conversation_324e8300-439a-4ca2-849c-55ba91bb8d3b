// التحكم في نافذة تسجيل الدخول الجانبية

document.addEventListener('DOMContentLoaded', function() {
    // العناصر
    const loginButton = document.getElementById('loginButton');
    const sideLoginOverlay = document.getElementById('sideLoginOverlay');
    const sideLoginPanel = document.getElementById('sideLoginPanel');
    const sideLoginClose = document.getElementById('sideLoginClose');
    const passwordToggle = document.getElementById('passwordToggle');
    const passwordField = document.getElementById('password');

    // فتح النافذة الجانبية عند النقر على زر تسجيل الدخول
    if (loginButton) {
        loginButton.addEventListener('click', function(e) {
            e.preventDefault();
            openSideLogin();
        });
    }

    // إغلاق النافذة الجانبية عند النقر على زر الإغلاق
    if (sideLoginClose) {
        sideLoginClose.addEventListener('click', function() {
            closeSideLogin();
        });
    }

    // إغلاق النافذة الجانبية عند النقر خارجها
    if (sideLoginOverlay) {
        sideLoginOverlay.addEventListener('click', function(e) {
            if (e.target === sideLoginOverlay) {
                closeSideLogin();
            }
        });
    }

    // إظهار/إخفاء كلمة المرور
    if (passwordToggle && passwordField) {
        passwordToggle.addEventListener('click', function() {
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                passwordToggle.innerHTML = '<i class="bi bi-eye-slash"></i>';
            } else {
                passwordField.type = 'password';
                passwordToggle.innerHTML = '<i class="bi bi-eye"></i>';
            }
        });
    }

    // فتح النافذة الجانبية
    function openSideLogin() {
        if (sideLoginOverlay && sideLoginPanel) {
            sideLoginOverlay.style.display = 'block';
            setTimeout(function() {
                sideLoginPanel.classList.add('active');
            }, 10);
            document.body.style.overflow = 'hidden';
        }
    }

    // إغلاق النافذة الجانبية
    function closeSideLogin() {
        if (sideLoginOverlay && sideLoginPanel) {
            sideLoginPanel.classList.remove('active');
            setTimeout(function() {
                sideLoginOverlay.style.display = 'none';
            }, 300);
            document.body.style.overflow = '';
        }
    }

    // إغلاق النافذة الجانبية عند الضغط على زر ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sideLoginOverlay.style.display === 'block') {
            closeSideLogin();
        }
    });
});
