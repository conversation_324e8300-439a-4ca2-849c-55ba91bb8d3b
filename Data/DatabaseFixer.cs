using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Abayat.Data
{
    public static class DatabaseFixer
    {
        public static async Task FixShowInCarouselColumn(IConfiguration configuration, ILogger logger)
        {
            try
            {
                string connectionString = configuration.GetConnectionString("DefaultConnection");
                
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    logger.LogInformation("تم الاتصال بقاعدة البيانات بنجاح");

                    // التحقق من وجود العمود
                    bool columnExists = false;
                    using (var checkCommand = new SqlCommand(
                        @"SELECT COUNT(*) 
                          FROM INFORMATION_SCHEMA.COLUMNS 
                          WHERE TABLE_NAME = 'Products' 
                          AND COLUMN_NAME = 'ShowInCarousel'", connection))
                    {
                        var result = await checkCommand.ExecuteScalarAsync();
                        columnExists = Convert.ToInt32(result) > 0;
                    }

                    if (!columnExists)
                    {
                        // إضافة العمود إذا لم يكن موجودًا
                        using (var addCommand = new SqlCommand(
                            @"ALTER TABLE Products 
                              ADD ShowInCarousel BIT NOT NULL DEFAULT 0", connection))
                        {
                            await addCommand.ExecuteNonQueryAsync();
                            logger.LogInformation("تم إضافة عمود ShowInCarousel إلى جدول Products بنجاح");
                        }
                    }
                    else
                    {
                        logger.LogInformation("العمود ShowInCarousel موجود بالفعل في جدول Products");
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "حدث خطأ أثناء محاولة إصلاح عمود ShowInCarousel: {Message}", ex.Message);
                
                if (ex.InnerException != null)
                {
                    logger.LogError("الخطأ الداخلي: {InnerMessage}", ex.InnerException.Message);
                }
                
                throw; // إعادة رمي الاستثناء للتعامل معه في المستوى الأعلى
            }
        }
    }
}
