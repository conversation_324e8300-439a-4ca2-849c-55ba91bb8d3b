// وظائف لأزرار المنتجات (إضافة للسلة، المفضلة، المقارنة) - نسخة آمنة

// التحقق من وجود document قبل تنفيذ أي كود
if (typeof document === 'undefined') {
    console.error('document غير متوفر. لا يمكن تنفيذ الكود.');
} else {
    // تنفيذ الكود بعد تحميل الصفحة بالكامل
    if (typeof window !== 'undefined' && typeof window.addEventListener === 'function') {
        window.addEventListener('load', function() {
            console.log('تم تحميل ملف product-actions-safe.js');

            // تأخير التنفيذ لضمان تحميل جميع العناصر
            setTimeout(function() {
                try {
                    // تعريف متغيرات عالمية لمنع التحديثات المتكررة
                    window.isUpdatingCompareCount = false;
                    window.isUpdatingCompareButtons = false;

                    // تهيئة الأزرار بشكل آمن
                    safeInitButtons();

                    // تحديث العدادات بشكل آمن
                    safeUpdateCounters();
                } catch (e) {
                    console.error('خطأ في التهيئة الرئيسية:', e);
                }
            }, 1500); // زيادة التأخير لضمان تحميل جميع العناصر
        });
    } else {
        console.error('window.addEventListener غير متوفر. لا يمكن تنفيذ الكود.');
    }
}

// تهيئة الأزرار بشكل آمن
function safeInitButtons() {
    console.log('بدء تهيئة الأزرار بشكل آمن');

    // تهيئة أزرار السلة
    try {
        initCartButtonsSafe();
    } catch (e) {
        console.error('خطأ في تهيئة أزرار السلة:', e);
    }

    // تهيئة أزرار المقارنة
    try {
        initCompareButtonsSafe();
    } catch (e) {
        console.error('خطأ في تهيئة أزرار المقارنة:', e);
    }

    console.log('تم الانتهاء من تهيئة الأزرار بشكل آمن');
}

// تحديث العدادات بشكل آمن
function safeUpdateCounters() {
    console.log('بدء تحديث العدادات بشكل آمن');

    // تحديث عداد السلة
    try {
        updateCartCountSafe();
    } catch (e) {
        console.error('خطأ في تحديث عداد السلة:', e);
    }

    // تحديث عداد المقارنة
    try {
        updateCompareCountSafe();
    } catch (e) {
        console.error('خطأ في تحديث عداد المقارنة:', e);
    }

    console.log('تم الانتهاء من تحديث العدادات بشكل آمن');
}

// تهيئة أزرار السلة بشكل آمن
function initCartButtonsSafe() {
    console.log('تهيئة أزرار السلة بشكل آمن');

    // البحث عن أزرار السلة بشكل آمن
    var cartButtons = safeQuerySelector('.add-to-cart-btn');
    if (!cartButtons || cartButtons.length === 0) {
        console.log('لم يتم العثور على أزرار سلة');
        return;
    }

    console.log(`تم العثور على ${cartButtons.length} زر سلة`);

    // إضافة مستمع الحدث لكل زر بشكل آمن
    for (var i = 0; i < cartButtons.length; i++) {
        try {
            var button = cartButtons[i];
            if (!button) continue;

            // إزالة أي مستمعات أحداث سابقة
            safeRemoveEventListener(button, 'click', cartClickHandlerSafe);

            // إضافة مستمع الحدث الجديد
            safeAddEventListener(button, 'click', cartClickHandlerSafe);
        } catch (e) {
            console.error('خطأ في تهيئة زر السلة:', e);
        }
    }
}

// معالج النقر على زر السلة بشكل آمن
function cartClickHandlerSafe(e) {
    try {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }

        var button = this;
        if (!button) return false;

        var productId = button.getAttribute('data-product-id');
        if (!productId) return false;

        console.log('تم النقر على زر إضافة للسلة للمنتج رقم:', productId);

        // إضافة تأثير حركي للزر بشكل آمن
        safeAddClass(button, 'adding');

        // إرسال طلب إضافة المنتج للسلة
        addToCartSafe(productId, button);

        return false;
    } catch (e) {
        console.error('خطأ في معالج النقر على زر السلة:', e);
        return false;
    }
}

// إضافة منتج للسلة بشكل آمن
function addToCartSafe(productId, buttonElement) {
    try {
        // الحصول على رمز التحقق من CSRF
        var tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
        var token = tokenElement ? tokenElement.value : null;

        if (!token) {
            console.error('لم يتم العثور على رمز التحقق من CSRF');
            showToastSafe('حدث خطأ أثناء إضافة المنتج للسلة');
            safeRemoveClass(buttonElement, 'adding');
            return;
        }

        console.log('جاري إضافة المنتج للسلة:', productId);

        // إرسال طلب إضافة المنتج للسلة
        fetch('/Cart/AddToCart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': token
            },
            body: `productId=${productId}&quantity=1&__RequestVerificationToken=${token}`
        })
        .then(function(response) {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                showToastSafe('تمت إضافة المنتج إلى السلة');

                // تحديث عداد السلة
                updateCartCountSafe();

                // تحديث حالة أزرار السلة
                updateCartButtonsStateSafe(productId, true);

                console.log('تم تحديث السلة بنجاح');
            } else {
                console.error('خطأ في إضافة المنتج للسلة:', data);
                showToastSafe('حدث خطأ أثناء إضافة المنتج للسلة');
            }

            // إزالة تأثير الإضافة من الزر
            safeRemoveClass(buttonElement, 'adding');
        })
        .catch(function(error) {
            console.error('خطأ في طلب إضافة المنتج للسلة:', error);
            showToastSafe('حدث خطأ أثناء إضافة المنتج للسلة');

            // إزالة تأثير الإضافة من الزر
            safeRemoveClass(buttonElement, 'adding');
        });
    } catch (e) {
        console.error('خطأ في إضافة المنتج للسلة:', e);
        showToastSafe('حدث خطأ أثناء إضافة المنتج للسلة');

        // إزالة تأثير الإضافة من الزر
        if (buttonElement) {
            safeRemoveClass(buttonElement, 'adding');
        }
    }
}

// تحديث عداد السلة بشكل آمن
function updateCartCountSafe() {
    try {
        console.log('جاري تحديث عداد السلة بشكل آمن');

        // التحقق من وجود عنصر عداد السلة
        var cartBadge = document.querySelector('.cart-count');
        if (!cartBadge) {
            console.log('لم يتم العثور على عنصر عداد السلة');
            return;
        }

        // إرسال طلب للحصول على عدد عناصر السلة
        fetch('/Cart/GetCartItemsCount')
        .then(function(response) {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(function(data) {
            try {
                // حفظ القيمة القديمة للمقارنة
                var oldCount = parseInt(cartBadge.textContent) || 0;
                var newCount = data.count;

                // تحديث النص
                cartBadge.textContent = newCount;

                // إضافة تأثير حركي إذا تغيرت القيمة
                if (newCount !== oldCount) {
                    safeAddClass(cartBadge, 'bounce');
                    safeAddClass(cartBadge, 'badge-highlight');

                    // إزالة التأثير بعد فترة
                    setTimeout(function() {
                        safeRemoveClass(cartBadge, 'bounce');
                        safeRemoveClass(cartBadge, 'badge-highlight');
                    }, 1500);
                }

                console.log('تم تحديث عداد السلة:', newCount);
            } catch (e) {
                console.error('خطأ في معالجة بيانات عداد السلة:', e);
            }
        })
        .catch(function(error) {
            console.error('خطأ في تحديث عداد السلة:', error);
        });
    } catch (e) {
        console.error('خطأ في تحديث عداد السلة:', e);
    }
}

// تحديث حالة أزرار السلة بشكل آمن
function updateCartButtonsStateSafe(productId, isActive) {
    try {
        console.log(`تحديث حالة أزرار السلة للمنتج ${productId}، الحالة: ${isActive}`);

        // البحث عن أزرار السلة للمنتج المحدد
        var selector = `.add-to-cart-btn[data-product-id="${productId}"]`;
        var buttons = safeQuerySelector(selector);

        if (!buttons || buttons.length === 0) {
            console.log(`لم يتم العثور على أزرار سلة للمنتج ${productId}`);
            return;
        }

        // تحديث حالة كل زر
        for (var i = 0; i < buttons.length; i++) {
            try {
                var button = buttons[i];
                if (!button) continue;

                if (isActive) {
                    safeAddClass(button, 'added');

                    // إزالة الكلاس بعد فترة
                    setTimeout(function(btn) {
                        return function() {
                            safeRemoveClass(btn, 'added');
                        };
                    }(button), 2000);
                }
            } catch (e) {
                console.error('خطأ في تحديث حالة زر السلة:', e);
            }
        }
    } catch (e) {
        console.error('خطأ في تحديث حالة أزرار السلة:', e);
    }
}

// عرض رسالة توست بشكل آمن
function showToastSafe(message) {
    try {
        // إنشاء عنصر التوست إذا لم يكن موجودًا
        var toast = document.getElementById('toast-notification');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'toast-notification';
            toast.className = 'toast-notification';
            document.body.appendChild(toast);
        }

        // عرض الرسالة
        toast.textContent = message;
        safeAddClass(toast, 'toast-visible');

        // إخفاء الرسالة بعد 3 ثوان
        setTimeout(function() {
            safeRemoveClass(toast, 'toast-visible');
        }, 3000);
    } catch (e) {
        console.error('خطأ في عرض رسالة التوست:', e);
    }
}

// وظائف مساعدة آمنة

// استعلام عن عناصر DOM بشكل آمن
function safeQuerySelector(selector) {
    try {
        // التحقق من وجود document
        if (!document) {
            console.error('document غير متوفر');
            return [];
        }

        // التحقق من وجود querySelectorAll
        if (typeof document.querySelectorAll !== 'function') {
            console.error('document.querySelectorAll غير متوفر');
            return [];
        }

        return document.querySelectorAll(selector);
    } catch (e) {
        console.error(`خطأ في استعلام العناصر (${selector}):`, e);
        return [];
    }
}

// إضافة مستمع حدث بشكل آمن
function safeAddEventListener(element, event, handler) {
    try {
        if (element && typeof element.addEventListener === 'function') {
            element.addEventListener(event, handler);
        }
    } catch (e) {
        console.error('خطأ في إضافة مستمع الحدث:', e);
    }
}

// إزالة مستمع حدث بشكل آمن
function safeRemoveEventListener(element, event, handler) {
    try {
        if (element && typeof element.removeEventListener === 'function') {
            element.removeEventListener(event, handler);
        }
    } catch (e) {
        console.error('خطأ في إزالة مستمع الحدث:', e);
    }
}

// إضافة كلاس بشكل آمن
function safeAddClass(element, className) {
    try {
        if (element && element.classList && typeof element.classList.add === 'function') {
            element.classList.add(className);
        }
    } catch (e) {
        console.error(`خطأ في إضافة الكلاس (${className}):`, e);
    }
}

// إزالة كلاس بشكل آمن
function safeRemoveClass(element, className) {
    try {
        if (element && element.classList && typeof element.classList.remove === 'function') {
            element.classList.remove(className);
        }
    } catch (e) {
        console.error(`خطأ في إزالة الكلاس (${className}):`, e);
    }
}

// تم حذف وظائف المفضلة

// التحقق من وجود كلاس بشكل آمن
function safeHasClass(element, className) {
    try {
        return element && element.classList && element.classList.contains(className);
    } catch (e) {
        console.error(`خطأ في التحقق من وجود الكلاس (${className}):`, e);
        return false;
    }
}

// تهيئة أزرار المقارنة (ستتم إضافتها لاحقًا)
function initCompareButtonsSafe() {
    console.log('تهيئة أزرار المقارنة بشكل آمن - سيتم تنفيذها لاحقًا');
}

// تحديث عداد المقارنة (ستتم إضافتها لاحقًا)
function updateCompareCountSafe() {
    console.log('تحديث عداد المقارنة بشكل آمن - سيتم تنفيذه لاحقًا');
}
