// إزالة الشريط السفلي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إزالة أي شريط تنقل ثانوي يظهر بعد الشريط الرئيسي
    const mainNav = document.querySelector('.main-navigation');
    if (mainNav) {
        let nextElement = mainNav.nextElementSibling;
        while (nextElement && nextElement.tagName === 'NAV') {
            const elementToRemove = nextElement;
            nextElement = nextElement.nextElementSibling;
            elementToRemove.remove();
        }
    }

    // إزالة أي عنصر يحتوي على كلمة "جميع المنتجات" في شريط التنقل
    const allNavbars = document.querySelectorAll('nav:not(.main-navigation)');
    allNavbars.forEach(navbar => {
        if (navbar.textContent.includes('جميع المنتجات') || 
            navbar.textContent.includes('المخور') || 
            navbar.textContent.includes('الإكسسوارات') || 
            navbar.textContent.includes('بخور') || 
            navbar.textContent.includes('الأقمشة') || 
            navbar.textContent.includes('عبايات')) {
            navbar.remove();
        }
    });

    // إزالة أي شريط بنفسجي
    const purpleBars = document.querySelectorAll('.navbar.bg-purple, .navbar.bg-primary');
    purpleBars.forEach(bar => {
        bar.remove();
    });

    // إزالة أي شريط تنقل يظهر مباشرة بعد العنصر header
    const header = document.querySelector('header');
    if (header && header.nextElementSibling && 
        (header.nextElementSibling.tagName === 'NAV' || 
         header.nextElementSibling.classList.contains('navbar'))) {
        header.nextElementSibling.remove();
    }
});
