// تحديد الرابط النشط في القائمة
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على المسار الحالي
    const currentPath = window.location.pathname;
    const currentSearch = window.location.search;
    
    // تحديد جميع روابط القائمة
    const navLinks = document.querySelectorAll('.main-nav .nav-link');
    
    // إزالة الفئة النشطة من جميع الروابط
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // تحديد الرابط النشط بناءً على المسار الحالي
    if (currentPath.includes('/Products') && currentSearch.includes('category=')) {
        // استخراج اسم الفئة من المسار
        const categoryParam = new URLSearchParams(currentSearch).get('category');
        
        // تحديد الرابط المطابق للفئة
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.includes(`category=${categoryParam}`)) {
                link.classList.add('active');
            }
        });
    } else if (currentPath.includes('/Products') && !currentSearch.includes('category=')) {
        // إذا كنا في صفحة المنتجات بدون فئة محددة، فإن رابط "المتجر" هو النشط
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.endsWith('/Products') || href && href.endsWith('/Products/Index')) {
                link.classList.add('active');
            }
        });
    } else if (currentPath.includes('/Categories/Products/') || currentPath.includes('/Categories/Details/')) {
        // إذا كنا في صفحة فئة محددة، فإن رابط الفئة هو النشط
        // هنا نحتاج إلى معرفة اسم الفئة من البيانات المعروضة في الصفحة
        const categoryName = document.querySelector('.category-name')?.textContent?.trim();
        if (categoryName) {
            navLinks.forEach(link => {
                if (link.textContent.trim() === categoryName) {
                    link.classList.add('active');
                }
            });
        }
    } else if (currentPath === '/' || currentPath === '/Home' || currentPath === '/Home/Index') {
        // إذا كنا في الصفحة الرئيسية، فإن رابط "الرئيسية" هو النشط
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && (href === '/' || href.endsWith('/Home') || href.endsWith('/Home/Index'))) {
                link.classList.add('active');
            }
        });
    }
});
