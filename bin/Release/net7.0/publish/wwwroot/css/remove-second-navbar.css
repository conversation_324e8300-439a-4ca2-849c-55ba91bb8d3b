/* إخفاء الشريط الثاني الذي يظهر في الصورة */
.navbar + .navbar,
nav + nav,
header + nav:not(main),
.navbar ~ .navbar,
.navbar-expand-lg + .navbar-expand-lg,
.navbar-light + .navbar-light,
.navbar:nth-of-type(2),
nav:nth-of-type(2),
.navbar-expand-lg:nth-of-type(2) {
    display: none !important;
}

/* إخفاء الشريط البنفسجي الذي يحتوي على "جميع المنتجات" */
.navbar.bg-purple,
.navbar.bg-primary,
.navbar-light.bg-light:not(.main-navigation),
.navbar:not(.main-navigation) {
    display: none !important;
}

/* إخفاء أي شريط يحتوي على زر "جميع المنتجات" */
.navbar:has(a:contains("جميع المنتجات")),
.navbar:has(button:contains("جميع المنتجات")),
.navbar:has(div:contains("جميع المنتجات")) {
    display: none !important;
}

/* إخفاء الشريط الثاني بشكل محدد بناءً على الصورة */
header + .navbar-expand-lg,
.navbar-expand-lg:not(.main-navigation) {
    display: none !important;
}

/* إخفاء الشريط الذي يحتوي على روابط الفئات */
.navbar:has(a[href*="category="]),
.navbar:has(a[href*="المخور"]),
.navbar:has(a[href*="الإكسسوارات"]),
.navbar:has(a[href*="بخور"]),
.navbar:has(a[href*="الأقمشة"]),
.navbar:has(a[href*="عبايات"]) {
    display: none !important;
}

/* إخفاء الشريط الثاني بشكل محدد */
.navbar-expand-lg.navbar-light:not(.main-navigation) {
    display: none !important;
}
