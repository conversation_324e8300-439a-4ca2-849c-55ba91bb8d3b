/* أنماط صفحة المفضلة والمقارنة */

/* أنماط عامة للصفحات */
.wishlist-page-header,
.compare-page-header {
    margin-bottom: 2rem;
}

.wishlist-title,
.compare-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.wishlist-subtitle,
.compare-subtitle {
    color: var(--text-muted);
    font-size: 1rem;
}

/* أنماط بطاقات المنتجات في المفضلة */
.wishlist-products {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

/* أنماط أزرار المفضلة */
.wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: white;
    color: #ccc;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.wishlist-btn:hover,
.wishlist-btn.active {
    background-color: #6a0dad;
    color: white;
}

.wishlist-btn.active {
    color: #6a0dad;
}

.wishlist-btn.active:hover {
    background-color: #f8f9fa;
    color: #6a0dad;
}

/* أنماط جدول المقارنة */
.compare-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1.5rem;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.compare-table th,
.compare-table td {
    padding: 1rem;
    text-align: center;
    border: 1px solid #eee;
}

.compare-table th {
    background-color: #f8f9fa;
    font-weight: 700;
    color: var(--dark-color);
}

.compare-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.compare-product-cell {
    position: relative;
    min-width: 200px;
}

.compare-remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #f8f9fa;
    color: #999;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.compare-remove-btn:hover {
    background-color: #6a0dad;
    color: white;
}

.compare-product-img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    margin: 0 auto 1rem;
    display: block;
}

.compare-product-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.compare-product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.compare-product-actions {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

/* Estilos para el contador de productos */
.compare-products-count,
.wishlist-products-count {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
}

/* Estilos para mensajes de alerta */
.alert {
    border-radius: 0;
    border: none;
    padding: 1rem;
}

/* Animaciones */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Estilos responsivos */
@media (max-width: 768px) {
    .wishlist-products {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .compare-table {
        display: block;
        overflow-x: auto;
    }

    .compare-product-cell {
        min-width: 150px;
    }
}

/* Estilos para el botón de añadir a la lista de deseos */
.add-to-wishlist-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: white;
    color: #333;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.add-to-wishlist-btn:hover,
.add-to-wishlist-btn.active {
    background-color: #6a0dad;
    color: white;
    transform: translateY(-3px);
}

.add-to-wishlist-btn.active {
    background-color: #6a0dad !important;
    color: white !important;
}

/* Estilos para el botón de añadir a la comparación */
.add-to-compare-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: white;
    color: #333;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.add-to-compare-btn:hover,
.add-to-compare-btn.active {
    background-color: #6a0dad;
    color: white;
    transform: translateY(-3px);
}

.add-to-compare-btn.active {
    background-color: #6a0dad !important;
    color: white !important;
}

/* Estilos para los contadores */
.wishlist-count,
.compare-count {
    font-size: 0.7rem;
    background-color: #6a0dad;
    color: white;
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    z-index: 5;
}

/* Animación de pulso para los contadores */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 0.5s ease-in-out;
}
