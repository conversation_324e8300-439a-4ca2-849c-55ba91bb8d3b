/* عكس ترتيب العناصر في شريط التنقل */
.navbar-nav.main-nav {
    display: flex;
    flex-direction: row-reverse;
}

/* تعديل هوامش العناصر بعد عكس الترتيب */
.navbar-nav.main-nav .nav-item {
    margin-left: 10px;
    margin-right: 10px;
}

/* تعديل محاذاة النص بعد عكس الترتيب */
.navbar-nav.main-nav .nav-link {
    text-align: center;
}

/* عكس ترتيب العناصر في الهيدر - نقل الاستفسارات إلى اليمين والتسجيل والبحث والمفضلة والسلة إلى اليسار */
.header .row {
    flex-direction: row;
}

/* تبديل الأقسام في الهيدر */
.header .row .col-md-4:nth-child(1) {
    order: 1; /* القسم الأيسر (التسجيل والبحث والمفضلة والسلة) ينتقل إلى اليسار */
}

.header .row .col-md-4:nth-child(2) {
    order: 2; /* القسم الأوسط (الشعار) يبقى في الوسط */
}

.header .row .col-md-4:nth-child(3) {
    order: 3; /* القسم الأيمن (الاستفسارات) ينتقل إلى اليمين */
}

/* ترتيب العناصر في القسم الأيسر (التسجيل ثم البحث ثم المفضلة ثم السلة) */
.left-section .d-flex {
    flex-direction: row;
    justify-content: flex-start;
}

/* ترتيب العناصر داخل القسم الأيسر */
.left-section .login-register {
    order: 1; /* التسجيل أولاً */
    margin-right: 15px;
}

.left-section .icon-link:has(i.bi-search) {
    order: 2; /* البحث ثانياً */
}

.left-section .icon-link:has(i.bi-heart) {
    order: 3; /* المفضلة ثالثاً */
}

.left-section .cart-container {
    order: 4; /* السلة والسعر رابعاً */
}

/* تعديل هوامش العناصر في القسم الأيسر بعد عكس الترتيب */
.left-section .icon-link {
    margin-left: 15px !important;
    margin-right: 15px !important;
}

/* تعديل محاذاة النص في القسم الأيمن (الاستفسارات) */
.contact-info {
    text-align: right !important;
}

/* تعديل محاذاة النص في القسم الأيسر (التسجيل والبحث والمفضلة والسلة) */
.left-section {
    text-align: left !important;
}
