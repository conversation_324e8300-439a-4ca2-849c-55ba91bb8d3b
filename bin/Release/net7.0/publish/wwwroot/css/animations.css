/* تأثيرات حركية للعدادات */

/* تأثير النبض */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 0.5s ease-in-out;
}

/* تأثير القفز */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

.bounce {
    animation: bounce 0.8s ease-in-out;
}

/* تأثير الوميض */
@keyframes flash {
    0%, 50%, 100% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0.5;
    }
}

.flash {
    animation: flash 0.8s ease-in-out;
}

/* تأثير الإضافة للسلة */
@keyframes adding {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        background-color: rgba(106, 13, 173, 0.8);
        box-shadow: 0 0 10px rgba(106, 13, 173, 0.5);
    }
    100% {
        transform: scale(1);
    }
}

.adding {
    animation: adding 0.8s ease-in-out;
    pointer-events: none;
}

/* تأثير تمت الإضافة */
@keyframes added {
    0% {
        background-color: rgba(106, 13, 173, 0.1);
        transform: scale(1);
    }
    50% {
        background-color: rgba(106, 13, 173, 0.8);
        transform: scale(1.1);
        box-shadow: 0 0 10px rgba(106, 13, 173, 0.5);
    }
    100% {
        background-color: rgba(106, 13, 173, 0.1);
        transform: scale(1);
    }
}

.added {
    animation: added 1s ease-in-out;
    pointer-events: none;
}

/* تأثير الإزالة */
@keyframes removed {
    0% {
        background-color: rgba(255, 0, 0, 0.1);
        transform: scale(1);
    }
    50% {
        background-color: rgba(255, 0, 0, 0.3);
        transform: scale(0.9);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

.removed {
    animation: removed 0.8s ease-in-out;
    pointer-events: none;
}

/* تنسيق العدادات */
.cart-count, .wishlist-count, .compare-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #6a0dad;
    color: white;
    font-size: 12px;
    position: absolute;
    top: -8px;
    right: -8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    font-weight: bold;
    z-index: 100;
}

/* تنسيق أزرار المنتجات */
.add-to-cart-btn, .add-to-wishlist-btn, .add-to-compare-btn {
    position: relative;
    z-index: 20;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.add-to-cart-btn:hover, .add-to-wishlist-btn:hover, .add-to-compare-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

.add-to-wishlist-btn.active {
    background-color: rgba(106, 13, 173, 0.2);
    color: #6a0dad;
    box-shadow: 0 0 5px rgba(106, 13, 173, 0.5);
}

.add-to-compare-btn.active {
    background-color: rgba(106, 13, 173, 0.2);
    color: #6a0dad;
    box-shadow: 0 0 5px rgba(106, 13, 173, 0.5);
}
