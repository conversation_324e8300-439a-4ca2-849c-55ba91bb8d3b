using System.ComponentModel.DataAnnotations;

namespace Abayat.ViewModels
{
    public class CheckoutViewModel
    {
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [Display(Name = "الاسم الكامل")]
        [StringLength(100, ErrorMessage = "يجب أن يكون الاسم بين {2} و {1} حرفًا", MinimumLength = 3)]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Display(Name = "رقم الهاتف")]
        [RegularExpression(@"^[0-9]{8,15}$", ErrorMessage = "يرجى إدخال رقم هاتف صحيح (8 أرقام على الأقل)")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "العنوان مطلوب")]
        [Display(Name = "العنوان")]
        [StringLength(200, ErrorMessage = "يجب أن يكون العنوان بين {2} و {1} حرفًا", MinimumLength = 3)]
        public string Address { get; set; } = string.Empty;

        [Display(Name = "ملاحظات")]
        [StringLength(500, ErrorMessage = "يجب أن تكون الملاحظات أقل من {1} حرفًا")]
        public string? Notes { get; set; }
    }
}
