using Abayat.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Abayat.Services
{
    public interface IOrderService
    {
        Task<Order> CreateOrderFromCartAsync(string userId, string fullName, string phoneNumber, string address, string notes);
        Task<Order> CreateGuestOrderAsync(List<CartItem> cartItems, string fullName, string phoneNumber, string address, string notes);
        Task<Order> GetOrderByIdAsync(int orderId, string userId);
        Task<Order> GetOrderByIdAsync(int orderId); // للمدير فقط
        Task<List<Order>> GetUserOrdersAsync(string userId);
        Task<List<Order>> GetAllOrdersAsync();
        Task<Order> UpdateOrderStatusAsync(int orderId, OrderStatus status);
        Task<bool> CancelOrderAsync(int orderId, string userId);
    }
}
