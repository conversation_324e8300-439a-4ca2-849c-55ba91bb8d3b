using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Abayat.Models
{
    public class Order
    {
        [Key]
        public int Id { get; set; }

        // يمكن أن يكون UserId فارغًا للطلبات من الزوار غير المسجلين
        public string? UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual ApplicationUser? User { get; set; }

        // علامة لتحديد ما إذا كان الطلب من زائر غير مسجل
        public bool IsGuestOrder { get; set; } = false;

        [Required]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "رقم الهاتف")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "العنوان")]
        public string Address { get; set; } = string.Empty;

        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Required]
        [Display(Name = "إجمالي الطلب")]
        [Column(TypeName = "decimal(18, 2)")]
        public decimal TotalAmount { get; set; }

        [Required]
        [Display(Name = "حالة الطلب")]
        public OrderStatus Status { get; set; } = OrderStatus.Pending;

        [Display(Name = "تاريخ الطلب")]
        public DateTime OrderDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation property for order items
        public virtual ICollection<OrderItem>? OrderItems { get; set; }
    }

    public enum OrderStatus
    {
        [Display(Name = "قيد الانتظار")]
        Pending = 0,

        [Display(Name = "تم التأكيد")]
        Confirmed = 1,

        [Display(Name = "قيد التجهيز")]
        Processing = 2,

        [Display(Name = "تم الشحن")]
        Shipped = 3,

        [Display(Name = "تم التسليم")]
        Delivered = 4,

        [Display(Name = "تم الإلغاء")]
        Cancelled = 5
    }
}
