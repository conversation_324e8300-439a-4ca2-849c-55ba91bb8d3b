@model Abayat.ViewModels.CheckoutViewModel

@{
    ViewData["Title"] = "إتمام الطلب";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">أدخل بيانات الشحن لإتمام الطلب</p>
    </div>
</div>

<div class="container py-5">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <div class="form-card slide-in-right">
                <h2 class="form-title">بيانات الشحن</h2>

                <form action="/Orders/Create" method="post" id="checkoutForm">
                    @Html.AntiForgeryToken()
                    <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>

                    <div class="mb-3">
                        <label asp-for="FullName" class="form-label">الاسم الكامل</label>
                        <input asp-for="FullName" class="form-control" required />
                        <span asp-validation-for="FullName" class="field-validation-error"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="PhoneNumber" class="form-label">رقم الهاتف</label>
                        <input asp-for="PhoneNumber" class="form-control" required />
                        <span asp-validation-for="PhoneNumber" class="field-validation-error"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Address" class="form-label">العنوان</label>
                        <textarea asp-for="Address" class="form-control" rows="3" required></textarea>
                        <span asp-validation-for="Address" class="field-validation-error"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Notes" class="form-label">ملاحظات إضافية (اختياري)</label>
                        <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="Notes" class="field-validation-error"></span>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="confirmOrderBtn">
                            <i class="bi bi-check-circle me-2"></i> تأكيد الطلب
                        </button>
                        <a href="/Cart" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i> العودة للسلة
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="form-card slide-in-left">
                <h2 class="form-title">ملخص الطلب</h2>

                <div id="order-summary">
                    <!-- سيتم تحميل ملخص الطلب هنا -->
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function () {
            // تحميل ملخص الطلب
            $.ajax({
                url: '/Cart/GetCartItemsCount',
                type: 'GET',
                success: function (data) {
                    if (data.count === 0) {
                        window.location.href = '/Cart';
                    } else {
                        loadOrderSummary();
                    }
                }
            });

            function loadOrderSummary() {
                $.ajax({
                    url: '/Cart',
                    type: 'GET',
                    success: function (data) {
                        var cartItems = $(data).find('.table-responsive');
                        if (cartItems.length > 0) {
                            $('#order-summary').html(cartItems);
                            // إزالة عمود الإجراءات
                            $('#order-summary th:last-child, #order-summary td:last-child').remove();
                            // إزالة نموذج تحديث الكمية
                            $('#order-summary form').each(function () {
                                var quantity = $(this).find('input[name="quantity"]').val();
                                $(this).replaceWith('<span>' + quantity + '</span>');
                            });
                        } else {
                            window.location.href = '/Cart';
                        }
                    }
                });
            }
        });
    </script>
}
