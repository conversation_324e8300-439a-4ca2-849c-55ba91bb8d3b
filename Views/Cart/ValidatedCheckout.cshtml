@model Abayat.ViewModels.CheckoutViewModel

@{
    ViewData["Title"] = "إتمام الطلب";
}

<div class="container py-4">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-md-8">
            <div class="checkout-section">
                <h2 class="section-title">بيانات الشحن</h2>
                <form asp-controller="Orders" asp-action="Create" method="post" id="checkoutForm">
                    @Html.AntiForgeryToken()

                    <div class="form-group mb-3">
                        <label for="FullName" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="FullName" name="FullName" value="@(Model?.FullName ?? "")" required minlength="3" maxlength="100">
                        <div class="invalid-feedback">
                            يرجى إدخال الاسم الكامل (3 أحرف على الأقل)
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="PhoneNumber" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" id="PhoneNumber" name="PhoneNumber" value="@(Model?.PhoneNumber ?? "")" required pattern="[0-9]{8,15}">
                        <div class="invalid-feedback">
                            يرجى إدخال رقم هاتف صحيح (8 أرقام على الأقل)
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="Address" class="form-label">العنوان <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="Address" name="Address" rows="3" required minlength="3" maxlength="200">@(Model?.Address ?? "")</textarea>
                        <div class="invalid-feedback">
                            يرجى إدخال العنوان (3 أحرف على الأقل)
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="Notes" class="form-label">ملاحظات إضافية (اختياري)</label>
                        <textarea class="form-control" id="Notes" name="Notes" rows="3">@(Model?.Notes ?? "")</textarea>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="/Cart" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i> العودة للسلة
                        </a>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" id="validateBtn">
                                تحقق <i class="bi bi-check ms-2"></i>
                            </button>
                            <button type="button" class="btn btn-purple" id="confirmOrderBtn">
                                تأكيد الطلب <i class="bi bi-check-circle ms-2"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <div class="order-summary-section">
                <h2 class="section-title">ملخص الطلب</h2>
                <div id="order-summary" class="order-items">
                    <!-- سيتم تحميل ملخص الطلب هنا -->
                    <div class="text-center py-3">
                        <div class="spinner-border text-purple" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .section-title {
        color: #6a0dad;
        border-bottom: 2px solid rgba(106, 13, 173, 0.1);
        padding-bottom: 10px;
        margin-bottom: 20px;
        font-size: 1.5rem;
        font-weight: bold;
    }

    .checkout-section, .order-summary-section {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .order-items {
        max-height: 400px;
        overflow-y: auto;
    }

    .form-control:focus {
        border-color: #6a0dad;
        box-shadow: 0 0 0 0.25rem rgba(106, 13, 173, 0.25);
    }

    .btn-purple {
        background-color: #ff5722;
        border-color: #ff5722;
        color: white;
    }

    .btn-purple:hover {
        background-color: #e64a19;
        border-color: #e64a19;
        color: white;
    }

    .text-purple {
        color: #6a0dad;
    }

    .was-validated .form-control:invalid,
    .form-control.is-invalid {
        border-color: #dc3545;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: left calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
</style>

@section Scripts {
    <script>
        $(document).ready(function () {
            // تحميل ملخص الطلب
            $.ajax({
                url: '/Cart/GetCartItemsCount',
                type: 'GET',
                success: function (data) {
                    if (data.count === 0) {
                        window.location.href = '/Cart';
                    } else {
                        loadOrderSummary();
                    }
                }
            });

            function loadOrderSummary() {
                $.ajax({
                    url: '/Cart',
                    type: 'GET',
                    success: function (data) {
                        var cartItems = $(data).find('.table-responsive');
                        if (cartItems.length > 0) {
                            $('#order-summary').html(cartItems);
                            // إزالة عمود الإجراءات
                            $('#order-summary th:last-child, #order-summary td:last-child').remove();
                            // إزالة نموذج تحديث الكمية
                            $('#order-summary form').each(function () {
                                var quantity = $(this).find('input[name="quantity"]').val();
                                $(this).replaceWith('<span>' + quantity + '</span>');
                            });
                        } else {
                            window.location.href = '/Cart';
                        }
                    }
                });
            }

            // تعريف دالة التحقق من الحقول على مستوى النطاق العام
            window.validateFieldsOnly = function() {
                var form = document.getElementById('checkoutForm');
                var isValid = true;

                // إزالة جميع الفئات السابقة
                var inputs = form.querySelectorAll('input, textarea');
                inputs.forEach(function(input) {
                    input.classList.remove('is-invalid', 'is-valid');
                });

                // التحقق من جميع الحقول المطلوبة
                inputs.forEach(function(input) {
                    // التحقق من الحقل الفارغ
                    if (input.hasAttribute('required') && input.value.trim() === '') {
                        input.classList.add('is-invalid');
                        isValid = false;
                    }
                    // التحقق من الحد الأدنى للطول
                    else if (input.hasAttribute('minlength') && input.value.trim().length < parseInt(input.getAttribute('minlength'))) {
                        input.classList.add('is-invalid');
                        isValid = false;
                    }
                    // التحقق من نمط رقم الهاتف
                    else if (input.id === 'PhoneNumber' && !/^[0-9]{8,15}$/.test(input.value.trim())) {
                        input.classList.add('is-invalid');
                        isValid = false;
                    }
                    // إذا كان الحقل صحيحًا
                    else if (input.value.trim() !== '') {
                        input.classList.add('is-valid');
                    }
                });

                // إظهار الحقول الخاطئة بوضوح
                if (!isValid) {
                    // تحريك الصفحة إلى أول حقل خاطئ
                    var firstInvalidField = form.querySelector('.is-invalid');
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }

                    // إظهار رسالة تنبيه
                    alert('يرجى التحقق من صحة البيانات المدخلة');
                }

                return isValid;
            };

            // تعريف دالة التحقق من صحة النموذج وإرساله على مستوى النطاق العام
            window.validateCheckoutForm = function() {
                // استخدام دالة التحقق من الحقول
                var isValid = window.validateFieldsOnly();

                // إذا كان النموذج صحيحًا، قم بتعطيل الزر وإظهار مؤشر التحميل
                if (isValid) {
                    $('#confirmOrderBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري التنفيذ...');

                    // إرسال النموذج مباشرة بدون تأخير
                    if (confirm('هل أنت متأكد من إتمام الطلب؟ سيتم حفظ الطلب في قاعدة البيانات.')) {
                        // إرسال النموذج باستخدام jQuery
                        $.ajax({
                            url: $('#checkoutForm').attr('action'),
                            type: 'POST',
                            data: $('#checkoutForm').serialize(),
                            dataType: 'json',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            success: function(response) {
                                // التعامل مع الاستجابة كـ JSON
                                if (response.success) {
                                    // إذا كانت العملية ناجحة، انتقل إلى صفحة نجاح الطلب
                                    window.location.href = '/Orders/OrderSuccess?id=' + response.orderId;
                                } else {
                                    // إذا كان هناك خطأ، عرض رسالة الخطأ
                                    alert(response.message || 'حدث خطأ أثناء معالجة الطلب');

                                    // إذا كانت هناك أخطاء محددة، قم بتمييز الحقول الخاطئة
                                    if (response.errors) {
                                        // إزالة جميع الفئات السابقة
                                        var inputs = document.querySelectorAll('input, textarea');
                                        inputs.forEach(function(input) {
                                            input.classList.remove('is-invalid', 'is-valid');
                                        });

                                        // تمييز الحقول الخاطئة
                                        for (var field in response.errors) {
                                            var input = document.getElementById(field);
                                            if (input) {
                                                input.classList.add('is-invalid');
                                            }
                                        }

                                        // تحريك الصفحة إلى أول حقل خاطئ
                                        var firstInvalidField = document.querySelector('.is-invalid');
                                        if (firstInvalidField) {
                                            firstInvalidField.focus();
                                            firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                        }
                                    }

                                    $('#confirmOrderBtn').prop('disabled', false).html('تأكيد الطلب');
                                }
                            },
                            error: function() {
                                alert('حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.');
                                $('#confirmOrderBtn').prop('disabled', false).html('تأكيد الطلب');
                            }
                        });
                    } else {
                        // إعادة تفعيل الزر إذا ألغى المستخدم العملية
                        $('#confirmOrderBtn').prop('disabled', false).html('تأكيد الطلب');
                    }
                }

                return isValid;
            };

            // إضافة مستمعي الأحداث للأزرار
            $('#validateBtn').on('click', function() {
                window.validateFieldsOnly();
            });

            $('#confirmOrderBtn').on('click', function() {
                window.validateCheckoutForm();
            });
        });
    </script>
}
