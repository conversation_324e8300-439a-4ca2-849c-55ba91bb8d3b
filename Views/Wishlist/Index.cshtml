@model List<Abayat.Models.Product>
@{
    ViewData["Title"] = "المنتجات المفضلة";
}

@* Token antifalsificación para solicitudes AJAX *@
@Html.AntiForgeryToken()

<div class="wishlist-container container">
    <h1 class="wishlist-title">@ViewData["Title"]</h1>
    <p class="wishlist-subtitle">قائمة المنتجات التي أضفتها إلى المفضلة</p>

    @if (Model != null && Model.Any())
    {
        <div class="wishlist-header mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="products-count">
                    عدد المنتجات: <span>@Model.Count</span>
                </div>
                <div class="view-options">
                    <div class="view-mode">
                        <div class="view-mode-option" data-view="grid-9" title="عرض 9 منتجات في الصف">
                            <i class="bi bi-grid-3x3-gap-fill"></i>
                        </div>
                        <div class="view-mode-option" data-view="grid-4" title="عرض 4 منتجات في الصف">
                            <i class="bi bi-grid-fill"></i>
                        </div>
                        <div class="view-mode-option active" data-view="grid-2" title="عرض منتجين في الصف">
                            <i class="bi bi-layout-split"></i>
                        </div>
                    </div>
                </div>
                <a href="@Url.Action("Index", "Products")" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-right-circle"></i>
                    العودة إلى المنتجات
                </a>
            </div>
        </div>

        <div class="product-grid">
            @foreach (var product in Model)
            {
                <div class="product-item animate__animated animate__fadeInUp">
                    <div class="product-item-img-container">
                        @if (!string.IsNullOrEmpty(product.ImageUrl))
                        {
                            <img src="@product.ImageUrl" class="product-item-img" alt="@product.Name">
                        }
                        else
                        {
                            <img src="~/images/no-image.png" class="product-item-img" alt="صورة غير متوفرة">
                        }

                        <div class="product-item-discount">-@product.DiscountPercentage%</div>

                        <div class="product-item-availability @(product.IsAvailable ? "available" : "not-available")">
                            @(product.IsAvailable ? "متوفر" : "غير متوفر")
                        </div>

                        <div class="product-item-actions">
                            <button class="product-item-action-btn add-to-cart-btn" data-product-id="@product.Id" title="إضافة للسلة">
                                <i class="bi bi-cart-plus"></i>
                            </button>
                            <button class="product-item-action-btn add-to-wishlist-btn active wishlist-active" data-product-id="@product.Id" title="إزالة من المفضلة">
                                <i class="bi bi-heart-fill"></i>
                            </button>
                            <button class="product-item-action-btn add-to-compare-btn" data-product-id="@product.Id" title="إضافة للمقارنة">
                                <i class="bi bi-arrow-left-right"></i>
                            </button>
                        </div>
                    </div>
                    <div class="product-item-info">
                        <h3 class="product-item-title">@product.Name</h3>
                        <div class="product-item-price">
                            <div class="product-item-price-current">@product.DiscountedPrice.ToString("N0") ر.ع</div>
                            <div class="product-item-price-original">@product.Price.ToString("N0") ر.ع</div>
                        </div>
                    </div>
                    <a href="/Products/Details/@product.Id" class="product-link"></a>
                </div>
            }
        </div>
    }
    else
    {
        <div class="empty-wishlist text-center">
            <div class="empty-wishlist-message">
                <i class="bi bi-heart display-1 text-muted mb-3"></i>
                <h3>لا يوجد منتجات في المفضلة</h3>
                <p>لم تقم بإضافة أي منتجات إلى المفضلة بعد</p>
                <a href="@Url.Action("Index", "Products")" class="btn btn-primary mt-3">
                    <i class="bi bi-bag"></i>
                    تصفح المنتجات
                </a>
            </div>
        </div>
    }
</div>

@section Styles {
    <link rel="stylesheet" href="~/css/products-page.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/product-display.css" asp-append-version="true" />
    <style>
        .wishlist-container {
            padding: 2rem 0;
        }

        .wishlist-title {
            color: #6a0dad;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .wishlist-subtitle {
            color: #666;
            margin-bottom: 2rem;
            text-align: center;
        }

        .empty-wishlist {
            padding: 3rem 0;
        }

        .empty-wishlist-message {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 3rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* تنسيق أزرار تغيير طريقة العرض */
        .view-mode {
            display: flex;
            gap: 5px;
        }

        .view-mode-option {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .view-mode-option:hover {
            background-color: #e9ecef;
        }

        .view-mode-option.active {
            background-color: #6a0dad;
            color: white;
        }

        /* تنسيق شبكة المنتجات */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .product-grid.grid-9 {
            grid-template-columns: repeat(9, 1fr);
        }

        .product-grid.grid-4 {
            grid-template-columns: repeat(4, 1fr);
        }

        .product-grid.grid-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        @* تنسيق للشاشات الكبيرة *@
        @@media (max-width: 1400px) {
            .product-grid.grid-9 {
                grid-template-columns: repeat(6, 1fr);
            }
        }

        @* تنسيق للشاشات المتوسطة *@
        @@media (max-width: 992px) {
            .product-grid.grid-9 {
                grid-template-columns: repeat(4, 1fr);
            }
            .product-grid.grid-4 {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @* تنسيق للشاشات الصغيرة *@
        @@media (max-width: 768px) {
            .product-grid,
            .product-grid.grid-9,
            .product-grid.grid-4 {
                grid-template-columns: repeat(1, 1fr);
            }
        }
    </style>
}

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // مسح التفضيلات المحفوظة سابقًا للتأكد من تطبيق التغييرات الجديدة
            document.cookie = "wishlistViewMode=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";

            // تم تعطيل تحديث عداد المفضلة مؤقتاً للإصلاح
            console.log('🚫 تم تعطيل تحديث عداد المفضلة في Wishlist/Index.cshtml DOMContentLoaded');

            // تهيئة أزرار المفضلة
            initWishlistButtons();

            // تهيئة أزرار تغيير طريقة العرض
            initViewControls();
        });

        // تهيئة أزرار تغيير طريقة العرض
        function initViewControls() {
            const productGrid = document.querySelector('.product-grid');
            const viewModeOptions = document.querySelectorAll('.view-mode-option');

            if (!productGrid || !viewModeOptions.length) return;

            // إضافة مستمعات الأحداث لأزرار تغيير طريقة العرض
            viewModeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const view = this.getAttribute('data-view');

                    // إزالة الفئة النشطة من جميع الأزرار
                    viewModeOptions.forEach(o => o.classList.remove('active'));

                    // إضافة الفئة النشطة للزر المحدد
                    this.classList.add('active');

                    // تغيير طريقة العرض
                    if (view === 'grid-9') {
                        productGrid.classList.remove('grid-4', 'grid-2');
                        productGrid.classList.add('grid-9');
                    } else if (view === 'grid-4') {
                        productGrid.classList.remove('grid-9', 'grid-2');
                        productGrid.classList.add('grid-4');
                    } else if (view === 'grid-2') {
                        productGrid.classList.remove('grid-9', 'grid-4');
                        productGrid.classList.add('grid-2');
                    }

                    // حفظ التفضيل في التخزين المحلي
                    localStorage.setItem('wishlistViewMode', view);

                    // عرض رسالة توست
                    showToast('تم تغيير طريقة العرض');
                });
            });

            // تحميل التفضيل المحفوظ
            const savedView = localStorage.getItem('wishlistViewMode') || 'grid-2';
            const savedViewOption = document.querySelector(`.view-mode-option[data-view="${savedView}"]`);

            if (savedViewOption) {
                savedViewOption.click();
            }
        }

        // تهيئة أزرار المفضلة في صفحة المفضلة
        function initWishlistButtons() {
            const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');

            wishlistButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    const productItem = this.closest('.product-item');

                    // إضافة تأثير حركي للزر
                    this.classList.add('adding');

                    // إرسال طلب AJAX لإضافة/إزالة المنتج من المفضلة
                    $.ajax({
                        url: '/Wishlist/AddToWishlist',
                        type: 'POST',
                        data: {
                            productId: productId,
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: (response) => {
                            if (response.success) {
                                if (!response.isInWishlist) {
                                    // إزالة المنتج من المفضلة
                                    if (productItem) {
                                        productItem.classList.add('animate__fadeOutUp');
                                        setTimeout(() => {
                                            productItem.remove();
                                            updateProductCount();
                                        }, 500);
                                    }
                                    showToast('تمت إزالة المنتج من المفضلة');
                                }

                                // تم تعطيل تحديث عداد المفضلة مؤقتاً للإصلاح
                                console.log('🚫 تم تعطيل تحديث عداد المفضلة مباشرة في Wishlist/Index.cshtml');

                                // لا نطلق حدث تحديث المفضلة لتجنب التحديثات المتكررة
                                // العداد تم تحديثه مباشرة أعلاه

                                // إذا لم يتبق أي منتجات، عرض رسالة "لا يوجد منتجات" بدون إعادة تحميل الصفحة
                                if (response.count === 0) {
                                    // إخفاء شبكة المنتجات
                                    const productGrid = document.querySelector('.product-grid');
                                    if (productGrid) {
                                        productGrid.style.display = 'none';
                                    }

                                    // إخفاء رأس الصفحة مع أزرار تغيير طريقة العرض
                                    const wishlistHeader = document.querySelector('.wishlist-header');
                                    if (wishlistHeader) {
                                        wishlistHeader.style.display = 'none';
                                    }

                                    // إظهار رسالة "لا يوجد منتجات"
                                    let emptyMessage = document.querySelector('.empty-wishlist');
                                    if (!emptyMessage) {
                                        emptyMessage = document.createElement('div');
                                        emptyMessage.className = 'empty-wishlist text-center';
                                        emptyMessage.innerHTML = `
                                            <div class="empty-wishlist-message">
                                                <i class="bi bi-heart display-1 text-muted mb-3"></i>
                                                <h3>لا يوجد منتجات في المفضلة</h3>
                                                <p>لم تقم بإضافة أي منتجات إلى المفضلة بعد</p>
                                                <a href="/Products/Index" class="btn btn-primary mt-3">
                                                    <i class="bi bi-bag"></i>
                                                    تصفح المنتجات
                                                </a>
                                            </div>
                                        `;
                                        document.querySelector('.wishlist-container').appendChild(emptyMessage);
                                    } else {
                                        emptyMessage.style.display = 'block';
                                    }
                                }
                            } else {
                                showToast('حدث خطأ أثناء تحديث المفضلة');
                            }

                            // إزالة تأثير الإضافة
                            this.classList.remove('adding');
                        },
                        error: () => {
                            showToast('حدث خطأ أثناء تحديث المفضلة');
                            this.classList.remove('adding');
                        }
                    });
                });
            });
        }

        // تحديث عدد المنتجات
        function updateProductCount() {
            const productItems = document.querySelectorAll('.product-item');
            const countElement = document.querySelector('.products-count span');

            if (countElement) {
                countElement.textContent = productItems.length;
            }
        }

        // عرض رسالة توست
        function showToast(message) {
            // إنشاء عنصر التوست إذا لم يكن موجودًا
            let toast = document.getElementById('toast-notification');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-notification';
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.right = '20px';
                toast.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '12px 20px';
                toast.style.borderRadius = '4px';
                toast.style.zIndex = '1000';
                toast.style.transition = 'opacity 0.5s ease-in-out';
                toast.style.opacity = '0';
                toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                document.body.appendChild(toast);
            }

            // عرض الرسالة
            toast.textContent = message;
            toast.style.opacity = '1';

            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                toast.style.opacity = '0';
            }, 3000);
        }
    </script>
}
