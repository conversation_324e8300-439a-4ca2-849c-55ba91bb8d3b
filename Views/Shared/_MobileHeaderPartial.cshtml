<!-- الهيدر في وضع الهاتف -->
<div class="mobile-header">
    <div class="container">
        <div class="mobile-header-container">
            <!-- زر السلة على اليمين -->
            <div class="mobile-header-cart">
                <a href="javascript:void(0);" onclick="goToCart(event)" class="icon-link">
                    <i class="bi bi-cart3"></i>
                    <span class="badge rounded-pill bg-danger cart-count mobile-badge">0</span>
                </a>
            </div>

            <!-- اسم المتجر في الوسط -->
            <div class="mobile-header-logo">
                <a asp-controller="Home" asp-action="Index">
                    <img src="~/images/logo.png" alt="راعي المخور" class="mobile-logo-img" />
                </a>
            </div>

            <!-- زر القائمة على اليسار -->
            <div class="mobile-header-menu">
                <button type="button" class="mobile-menu-button" onclick="openMobileSidebar()">
                    <i class="bi bi-list"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- النافذة الجانبية للقائمة في وضع الهاتف -->
<div id="mobileSidebarOverlay" class="mobile-sidebar-overlay">
    <div id="mobileSidebarPanel" class="mobile-sidebar-panel">
        <!-- رأس النافذة الجانبية -->
        <div class="mobile-sidebar-header">
            <button type="button" class="mobile-sidebar-close" id="mobileSidebarClose">
                <i class="bi bi-x-lg"></i>
            </button>
            <h5 class="mobile-sidebar-title-main">القائمة</h5>
        </div>

        <!-- محتوى النافذة الجانبية -->
        <div class="mobile-sidebar-content">


               <!-- قسم المنتجات والفئات -->
            <div class="mobile-sidebar-section">
                <h6 class="mobile-sidebar-title purple-title">المنتجات</h6>
                <ul class="mobile-sidebar-list products-categories-list">
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" asp-controller="Home" asp-action="Index">
                            <i class="bi bi-house-door"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" asp-controller="Products" asp-action="Index">
                            <i class="bi bi-shop"></i>
                            <span>المتجر</span>
                        </a>
                    </li>
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" asp-controller="Products" asp-action="Index" asp-route-category="عبايات">
                            <i class="bi bi-tag"></i>
                            <span>عبايات</span>
                        </a>
                    </li>
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" asp-controller="Products" asp-action="Index" asp-route-category="الأقمشة">
                            <i class="bi bi-tag"></i>
                            <span>الأقمشة</span>
                        </a>
                    </li>
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" asp-controller="Products" asp-action="Index" asp-route-category="بخور">
                            <i class="bi bi-tag"></i>
                            <span>بخور</span>
                        </a>
                    </li>
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" asp-controller="Products" asp-action="Index" asp-route-category="الإكسسوارات">
                            <i class="bi bi-tag"></i>
                            <span>الإكسسوارات</span>
                        </a>
                    </li>
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" asp-controller="Products" asp-action="Index" asp-route-category="المخور">
                            <i class="bi bi-tag"></i>
                            <span>المخور</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- قسم الأدوات -->
            <div class="mobile-sidebar-section">
                <h6 class="mobile-sidebar-title purple-title">الأدوات</h6>
                <ul class="mobile-sidebar-list tools-list">
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" href="javascript:void(0);" onclick="goToWishlist(event)">
                            <i class="bi bi-heart"></i>
                            <span>المفضلة</span>
                        </a>
                    </li>
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" href="javascript:void(0);" onclick="goToCompare(event)">
                            <i class="bi bi-arrow-left-right"></i>
                            <span>المقارنة</span>
                        </a>
                    </li>
                    <li class="mobile-sidebar-item">
                        <a class="mobile-sidebar-link" href="javascript:void(0);" onclick="goToCart(event)">
                            <i class="bi bi-cart3"></i>
                            <span>السلة</span>
                        </a>
                    </li>
                </ul>
            </div>


            <!-- قسم الحساب -->
            @if (User.Identity?.IsAuthenticated == true)
            {
                <div class="mobile-sidebar-section">
                    <ul class="mobile-sidebar-list">
                        <li class="mobile-sidebar-item">
                            <a class="mobile-sidebar-link" asp-controller="Orders" asp-action="Index">
                                <i class="bi bi-bag"></i>
                                <span>طلباتي</span>
                            </a>
                        </li>
                        <li class="mobile-sidebar-item">
                            <a class="mobile-sidebar-link" asp-area="Identity" asp-page="/Account/Manage/Index">
                                <i class="bi bi-gear"></i>
                                <span>إعدادات الحساب</span>
                            </a>
                        </li>
                        @if (User.IsInRole("Admin"))
                        {
                            <li class="mobile-sidebar-item">
                                <a class="mobile-sidebar-link" asp-controller="Admin" asp-action="Index">
                                    <i class="bi bi-speedometer2"></i>
                                    <span>لوحة التحكم</span>
                                </a>
                            </li>
                        }
                        <li class="mobile-sidebar-item">
                            <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                                <button type="submit" class="mobile-sidebar-link logout-button">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>تسجيل الخروج</span>
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            }
            else
            {
                <div class="mobile-sidebar-section">
                    <ul class="mobile-sidebar-list">
                        <li class="mobile-sidebar-item">
                            <a class="mobile-sidebar-link" href="#" onclick="openSideLogin(); return false;">
                                <i class="bi bi-person"></i>
                                <span>تسجيل الدخول</span>
                            </a>
                        </li>
                    </ul>
                </div>
            }
        </div>
    </div>
</div>
