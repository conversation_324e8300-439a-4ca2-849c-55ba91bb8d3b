<!-- شريط التنقل العلوي القابل للاختفاء -->
<div class="sticky-nav">
    <!-- نسخة الشاشات الكبيرة -->
    <div class="sticky-nav-desktop d-none d-md-block">
        <div class="container">
            <div class="sticky-nav-container">
                <!-- الشعار -->
                <div class="sticky-nav-logo">
                    <a asp-controller="Home" asp-action="Index">
                        <img src="~/images/logo.png" alt="راعي المخور" class="sticky-logo-img" />
                    </a>
                </div>

                <!-- روابط التنقل الرئيسية -->
                <div class="sticky-nav-links">
                    <a asp-controller="Home" asp-action="Index" class="sticky-nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")">الرئيسية</a>
                    <a asp-controller="Products" asp-action="Index" class="sticky-nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Products" && ViewContext.RouteData.Values["category"] == null ? "active" : "")">المتجر</a>
                    @await Component.InvokeAsync("CategoriesForNav")
                    <a asp-controller="Home" asp-action="About" class="sticky-nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" && ViewContext.RouteData.Values["Action"]?.ToString() == "About" ? "active" : "")">عن المتجر</a>
                    <a asp-controller="Home" asp-action="Contact" class="sticky-nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" && ViewContext.RouteData.Values["Action"]?.ToString() == "Contact" ? "active" : "")">اتصل بنا</a>
                </div>

                <!-- أيقونات الهيدر -->
                <div class="sticky-nav-icons">
                    <!-- البحث -->
                    <a href="#" class="sticky-icon-link">
                        <i class="bi bi-search"></i>
                    </a>

                    <!-- المفضلة -->
                    <a href="javascript:void(0);" onclick="goToWishlist(event)" class="sticky-icon-link">
                        <i class="bi bi-heart"></i>
                        <span class="badge rounded-pill bg-danger wishlist-count sticky-badge">0</span>
                    </a>

                    <!-- المقارنة -->
                    <a href="javascript:void(0);" onclick="goToCompare(event)" class="sticky-icon-link">
                        <i class="bi bi-arrow-left-right"></i>
                        <span class="badge rounded-pill bg-danger compare-count sticky-badge">0</span>
                    </a>

                    <!-- السلة -->
                    <a href="javascript:void(0);" onclick="goToCart(event)" class="sticky-icon-link">
                        <i class="bi bi-cart3"></i>
                        <span class="badge rounded-pill bg-danger cart-count sticky-badge">0</span>
                    </a>

                    <!-- تسجيل الدخول -->
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <div class="dropdown">
                            <a class="sticky-icon-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-fill"></i>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" asp-controller="Orders" asp-action="Index">
                                        <i class="bi bi-bag"></i> طلباتي
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                                        <i class="bi bi-gear"></i> إعدادات الحساب
                                    </a>
                                </li>
                                @if (User.IsInRole("Admin"))
                                {
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" asp-controller="Admin" asp-action="Index">
                                            <i class="bi bi-speedometer2"></i> لوحة التحكم
                                        </a>
                                    </li>
                                }
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })" method="post">
                                        @Html.AntiForgeryToken()
                                        <button type="submit" class="dropdown-item">
                                            <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    }
                    else
                    {
                        <a href="#" onclick="openSideLogin(); return false;" class="sticky-icon-link">
                            <i class="bi bi-person"></i>
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- نسخة الهاتف المحمول -->
    <div class="sticky-nav-mobile d-block d-md-none">
        <div class="container">
            <div class="sticky-mobile-nav-container">
                <!-- السلة (على اليسار) -->
                <div class="sticky-mobile-cart">
                    <a href="javascript:void(0);" onclick="goToCart(event)">
                        <i class="bi bi-cart3"></i>
                        <span class="badge rounded-pill bg-danger cart-count sticky-badge">0</span>
                    </a>
                </div>

                <!-- الشعار -->
                <div class="sticky-mobile-logo">
                    <a asp-controller="Home" asp-action="Index">
                        <img src="~/images/logo.png" alt="راعي المخور" class="sticky-mobile-logo-img" />
                    </a>
                </div>

                <!-- زر القائمة (على اليمين) -->
                <div class="sticky-mobile-menu-btn">
                    <a href="#" onclick="openMobileSidebar(); return false;">
                        <i class="bi bi-list"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
