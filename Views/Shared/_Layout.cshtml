<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - راعي المخور</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/cart.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/display-options.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/order-tracking.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/wishlist-compare.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/product-display.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/animations.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/navbar-custom.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/navbar-font.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/header-ameera.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/reverse-navbar-items.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/badge-position-fix.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/login-register-buttons.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/header-icons.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/side-login.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/rtl-fixes.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-nav.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-menu.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-header.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/side-filter.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/sticky-nav.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/carousel-custom.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/admin-dashboard.css" asp-append-version="true" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Reem+Kufi:wght@400;500;600;700&family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">

    <!-- خط Tajawal للقائمة -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي القابل للاختفاء -->
    @await Html.PartialAsync("_StickyNavPartial")

    <header>
        <!-- الهيدر العادي للشاشات الكبيرة -->
        @await Html.PartialAsync("_HeaderPartial")

        <!-- الهيدر في وضع الهاتف للشاشات الصغيرة -->
        @await Html.PartialAsync("_MobileHeaderPartial")
    </header>

    <!-- شريط القوائم الرئيسي -->
    @await Html.PartialAsync("_NavbarPartial")

    <!-- نافذة تسجيل الدخول الجانبية -->
    <div id="sideLoginOverlay" class="side-login-overlay">
        <div id="sideLoginPanel" class="side-login-panel">
            <!-- قسم تسجيل الدخول -->
            <div id="loginSection" class="side-panel-section">
                <div class="side-login-header">
                    <button id="sideLoginClose" class="side-login-close">
                        <i class="bi bi-x-lg"></i> قريب
                    </button>
                    <h2>تسجيل الدخول</h2>
                </div>
                <div class="side-login-body">
                    <form class="side-login-form" asp-area="Identity" asp-page="/Account/Login" method="post">
                        <div class="form-group">
                            <label for="username">
                                <span class="required">*</span> اسم المستخدم أو البريد الإلكتروني
                            </label>
                            <input type="text" id="username" name="Input.Email" class="form-control" required />
                        </div>
                        <div class="form-group">
                            <label for="password">
                                <span class="required">*</span> كلمة المرور
                            </label>
                            <div class="password-field">
                                <input type="password" id="password" name="Input.Password" class="form-control" required />
                                <button type="button" id="passwordToggle" class="password-toggle">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">تسجيل الدخول</button>
                        <div class="form-footer">
                            <div class="remember-me">
                                <label for="remember">تذكرني</label>
                                <input type="checkbox" id="remember" name="Input.RememberMe" />
                            </div>
                            <a href="javascript:void(0);" id="showForgotPassword" class="forgot-password">فقدت كلمة المرور الخاصة بك ؟</a>
                        </div>
                    </form>
                    <div class="side-login-divider"></div>
                    <div class="side-login-register">
                        <div class="register-icon">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <p>أي حساب حتى الآن ؟</p>
                        <a href="javascript:void(0);" id="showRegister">إنشاء حساب</a>
                    </div>
                </div>
            </div>

            <!-- قسم استعادة كلمة المرور -->
            <div id="forgotPasswordSection" class="side-panel-section" style="display: none;">
                <div class="side-login-header">
                    <button class="side-login-back" id="backToLogin1">
                        <i class="bi bi-arrow-right"></i> رجوع
                    </button>
                    <h2>استعادة كلمة المرور</h2>
                </div>
                <div class="side-login-body">
                    <form class="side-login-form" asp-area="Identity" asp-page="/Account/ForgotPassword" method="post">
                        <div class="form-group">
                            <label for="forgotEmail">
                                <span class="required">*</span> البريد الإلكتروني
                            </label>
                            <input type="email" id="forgotEmail" name="Input.Email" class="form-control" required />
                        </div>
                        <p class="text-muted">أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور الخاصة بك.</p>
                        <button type="submit" class="submit-btn">إرسال</button>
                    </form>
                </div>
            </div>

            <!-- قسم التسجيل الجديد -->
            <div id="registerSection" class="side-panel-section" style="display: none;">
                <div class="side-login-header">
                    <button class="side-login-back" id="backToLogin2">
                        <i class="bi bi-arrow-right"></i> رجوع
                    </button>
                    <h2>إنشاء حساب جديد</h2>
                </div>
                <div class="side-login-body">
                    <form class="side-login-form" asp-area="Identity" asp-page="/Account/Register" method="post">
                        <div class="form-group">
                            <label for="registerEmail">
                                <span class="required">*</span> البريد الإلكتروني
                            </label>
                            <input type="email" id="registerEmail" name="Input.Email" class="form-control" required />
                        </div>
                        <div class="form-group">
                            <label for="registerPassword">
                                <span class="required">*</span> كلمة المرور
                            </label>
                            <div class="password-field">
                                <input type="password" id="registerPassword" name="Input.Password" class="form-control" required />
                                <button type="button" id="registerPasswordToggle" class="password-toggle">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="registerConfirmPassword">
                                <span class="required">*</span> تأكيد كلمة المرور
                            </label>
                            <div class="password-field">
                                <input type="password" id="registerConfirmPassword" name="Input.ConfirmPassword" class="form-control" required />
                                <button type="button" id="registerConfirmPasswordToggle" class="password-toggle">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">تسجيل</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <main role="main">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show text-center" role="alert" style="margin-bottom: 0;">
                <strong><i class="bi bi-check-circle-fill"></i></strong> @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show text-center" role="alert" style="margin-bottom: 0;">
                <strong><i class="bi bi-exclamation-triangle-fill"></i></strong> @TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }

        <!-- رمز التحقق من CSRF للطلبات AJAX -->
        @Html.AntiForgeryToken()

        <div class="container py-4">
            <div class="row">
                @if (User.IsInRole("Admin") &&
                    (ViewContext.RouteData.Values["Controller"]?.ToString() == "Admin" ||
                     ViewContext.RouteData.Values["Controller"]?.ToString() == "CarouselImages"))
                {
                    <div class="col-md-3 d-none d-md-block">
                        <partial name="_AdminMenuPartial" />
                    </div>
                    <div class="col-md-9">
                        @RenderBody()
                    </div>
                }
                else
                {
                    <div class="col-12">
                        @RenderBody()
                    </div>
                }
            </div>
        </div>
    </main>

    <!-- شريط التنقل السفلي للهواتف المحمولة -->
    @await Html.PartialAsync("_MobileNavPartial")

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3 class="footer-title">راعي المخور</h3>
                    <p class="text-white-50">متجر متخصص في بيع العبايات والأقمشة والإكسسوارات والمخور بأعلى جودة وأفضل الأسعار.</p>
                    <div class="footer-social">
                        <a href="#"><i class="bi bi-facebook"></i></a>
                        <a href="#"><i class="bi bi-instagram"></i></a>
                        <a href="#"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-snapchat"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3 class="footer-title">روابط سريعة</h3>
                    @await Component.InvokeAsync("FooterCategories")
                </div>
                <div class="footer-column">
                    <h3 class="footer-title">الفئات</h3>
                    @await Component.InvokeAsync("FooterCategories")
                </div>
            </div>
            <div class="footer-bottom">
                &copy; @DateTime.Now.Year - جميع الحقوق محفوظة لراعي المخور
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- <script src="~/js/disable-console-updates.js" asp-append-version="true"></script> -->
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/modal-fix.js" asp-append-version="true"></script>
    <script src="~/js/display-options.js" asp-append-version="true"></script>
    <script src="~/js/active-link.js" asp-append-version="true"></script>
    <script src="~/js/side-login.js" asp-append-version="true"></script>
    <script src="~/js/mobile-sidebar.js" asp-append-version="true"></script>
    <script src="~/js/side-filter.js" asp-append-version="true"></script>
    <script src="~/js/sticky-nav.js" asp-append-version="true"></script>
    <script src="~/js/sticky-nav-active.js" asp-append-version="true"></script>

    <!-- استخدام النسخة الآمنة من ملف product-actions.js -->
    <script src="~/js/product-actions-safe.js" asp-append-version="true"></script>


    <script>
        // تحديث عدد العناصر عند تحميل الصفحة
        $(document).ready(function () {
            // استعادة العدادات من ملفات تعريف الارتباط أولاً
            restoreCountersFromCookies();

            // ثم تحديث جميع العدادات من الخادم
            updateAllCounters();

            // تحديث العدادات مرة واحدة فقط عند تحميل الصفحة
            // تم إيقاف التحديث الدوري
            // setInterval(updateAllCounters, 5000);
        });

        // وظيفة لتحديث جميع العدادات
        function updateAllCounters() {
            console.log('بدء تحديث جميع العدادات');

            // تحديث عداد المفضلة
            updateWishlistCounterDirectly();

            // تحديث عداد المقارنة
            updateCompareCounterDirectly();

            // تحديث عداد السلة
            updateCartCounterDirectly();

            // تحديث حالة الأزرار إذا كانت الوظائف متاحة
            if (typeof updateWishlistButtonsState === 'function') {
                updateWishlistButtonsState();
            }

            if (typeof updateCompareButtonsState === 'function') {
                updateCompareButtonsState();
            }

            // حفظ العدادات في ملفات تعريف الارتباط بعد التحديث
            setTimeout(function() {
                saveCountersToCookies();
            }, 1000);

            console.log('تم تحديث جميع العدادات');
        }

        // تحديث عداد المفضلة مباشرة
        function updateWishlistCounterDirectly() {
            $.ajax({
                url: '@Url.Action("GetWishlistItemsCount", "Wishlist")',
                type: 'GET',
                success: function (data) {
                    const wishlistBadge = $('.wishlist-count');
                    if (wishlistBadge.length) {
                        const oldCount = parseInt(wishlistBadge.text()) || 0;
                        const newCount = data.count || 0;

                        // تحديث النص (تأكد من أن القيمة رقم وليست نصاً غير صالح)
                        wishlistBadge.text(isNaN(newCount) ? '0' : newCount);

                        // إضافة تأثير حركي إذا تغيرت القيمة
                        if (newCount !== oldCount) {
                            applyCounterAnimation(wishlistBadge);
                        }

                        console.log('تم تحديث عداد المفضلة:', newCount);

                        // حفظ العداد في ملفات تعريف الارتباط
                        setCookie('wishlistCount', newCount, 30);
                    }
                },
                error: function(error) {
                    console.error('خطأ في تحديث عداد المفضلة:', error);
                },
                complete: function() {
                    // بعد الانتهاء من تحديث العداد، قم بتحديث حالة الأزرار
                    $.ajax({
                        url: '@Url.Action("GetWishlistItems", "Wishlist")',
                        type: 'GET',
                        success: function(data) {
                            if (data && data.items) {
                                $('.add-to-wishlist-btn').each(function() {
                                    const productId = parseInt($(this).data('product-id'));
                                    if (data.items.includes(productId)) {
                                        $(this).addClass('active');
                                    } else {
                                        $(this).removeClass('active');
                                    }
                                });
                                console.log('تم تحديث حالة أزرار المفضلة مباشرة');
                            }
                        }
                    });
                }
            });
        }

        // تحديث عداد المقارنة مباشرة
        function updateCompareCounterDirectly() {
            $.ajax({
                url: '@Url.Action("GetCompareItemsCount", "Compare")',
                type: 'GET',
                success: function (data) {
                    const compareBadge = $('.compare-count');
                    if (compareBadge.length) {
                        const oldCount = parseInt(compareBadge.text()) || 0;
                        const newCount = data.count || 0;

                        // تحديث النص (تأكد من أن القيمة رقم وليست نصاً غير صالح)
                        compareBadge.text(isNaN(newCount) ? '0' : newCount);

                        // إضافة تأثير حركي إذا تغيرت القيمة
                        if (newCount !== oldCount) {
                            applyCounterAnimation(compareBadge);
                        }

                        console.log('تم تحديث عداد المقارنة:', newCount);

                        // حفظ العداد في ملفات تعريف الارتباط
                        setCookie('compareCount', newCount, 30);
                    }
                },
                error: function(error) {
                    console.error('خطأ في تحديث عداد المقارنة:', error);
                },
                complete: function() {
                    // بعد الانتهاء من تحديث العداد، قم بتحديث حالة الأزرار
                    $.ajax({
                        url: '@Url.Action("GetCompareItems", "Compare")',
                        type: 'GET',
                        success: function(data) {
                            if (data && data.items) {
                                $('.add-to-compare-btn').each(function() {
                                    const productId = parseInt($(this).data('product-id'));
                                    if (data.items.includes(productId)) {
                                        $(this).addClass('active');
                                    } else {
                                        $(this).removeClass('active');
                                    }
                                });
                                console.log('تم تحديث حالة أزرار المقارنة مباشرة');
                            }
                        }
                    });
                }
            });
        }

        // تحديث عداد السلة مباشرة
        function updateCartCounterDirectly() {
            $.ajax({
                url: '@Url.Action("GetCartItemsCount", "Cart")',
                type: 'GET',
                success: function (data) {
                    const cartBadge = $('.cart-count');
                    if (cartBadge.length) {
                        const oldCount = parseInt(cartBadge.text()) || 0;
                        const newCount = data.count || 0;

                        // تحديث النص (تأكد من أن القيمة رقم وليست نصاً غير صالح)
                        cartBadge.text(isNaN(newCount) ? '0' : newCount);

                        // إضافة تأثير حركي إذا تغيرت القيمة
                        if (newCount !== oldCount) {
                            applyCounterAnimation(cartBadge);
                        }

                        console.log('تم تحديث عداد السلة:', newCount);

                        // حفظ العداد في ملفات تعريف الارتباط
                        setCookie('cartCount', newCount, 30);
                    }
                },
                error: function(error) {
                    console.error('خطأ في تحديث عداد السلة:', error);
                }
            });
        }

        // وظيفة لتطبيق تأثير حركي على العداد
        function applyCounterAnimation(element) {
            // إزالة أي تأثيرات سابقة
            element.removeClass('pulse bounce flash');

            // إضافة تأثير جديد
            element.addClass('bounce');

            // تغيير لون الخلفية مؤقتًا
            element.css('backgroundColor', '#6a0dad');

            // إعادة اللون الأصلي بعد فترة
            setTimeout(function() {
                element.removeClass('bounce');
                element.css('backgroundColor', '');
            }, 1500);
        }

        // استعادة العدادات من ملفات تعريف الارتباط (Cookies)
        function restoreCountersFromCookies() {
            try {
                // استعادة عداد المفضلة
                const wishlistCount = getCookie('wishlistCount') || '0';
                $('.wishlist-count').text(wishlistCount);

                // استعادة عداد المقارنة
                const compareCount = getCookie('compareCount') || '0';
                $('.compare-count').text(compareCount);

                // استعادة عداد السلة
                const cartCount = getCookie('cartCount') || '0';
                $('.cart-count').text(cartCount);

                console.log('تم استعادة العدادات من ملفات تعريف الارتباط');
            } catch (e) {
                console.error('خطأ في استعادة العدادات من ملفات تعريف الارتباط:', e);
            }
        }

        // حفظ العدادات في ملفات تعريف الارتباط (Cookies)
        function saveCountersToCookies() {
            try {
                // حفظ عداد المفضلة
                const wishlistCount = $('.wishlist-count').text() || '0';
                setCookie('wishlistCount', wishlistCount, 30); // تخزين لمدة 30 يوم

                // حفظ عداد المقارنة
                const compareCount = $('.compare-count').text() || '0';
                setCookie('compareCount', compareCount, 30); // تخزين لمدة 30 يوم

                // حفظ عداد السلة
                const cartCount = $('.cart-count').text() || '0';
                setCookie('cartCount', cartCount, 30); // تخزين لمدة 30 يوم

                console.log('تم حفظ العدادات في ملفات تعريف الارتباط');
            } catch (e) {
                console.error('خطأ في حفظ العدادات في ملفات تعريف الارتباط:', e);
            }
        }

        // وظيفة لتعيين ملف تعريف ارتباط
        function setCookie(name, value, days) {
            let expires = "";
            if (days) {
                const date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + value + expires + "; path=/";
        }

        // وظيفة للحصول على قيمة ملف تعريف ارتباط
        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }

        // إضافة مستمعات أحداث للأزرار
        $(document).on('wishlist:updated', function() {
            updateWishlistCounterDirectly();
            // حفظ العدادات بعد التحديث
            saveCountersToCookies();
        });

        $(document).on('compare:updated', function() {
            updateCompareCounterDirectly();
            // حفظ العدادات بعد التحديث
            saveCountersToCookies();
        });

        $(document).on('cart:updated', function() {
            updateCartCounterDirectly();
            // حفظ العدادات بعد التحديث
            saveCountersToCookies();
        });

        // وظيفة للانتقال إلى صفحة المفضلة بعد التأكد من تحديث العداد
        function goToWishlist(event) {
            event.preventDefault();

            // تحديث عداد المفضلة أولاً
            $.ajax({
                url: '@Url.Action("GetWishlistItemsCount", "Wishlist")',
                type: 'GET',
                success: function(data) {
                    const wishlistBadge = $('.wishlist-count');
                    if (wishlistBadge.length) {
                        // تحديث النص (تأكد من أن القيمة رقم وليست نصاً غير صالح)
                        const count = data.count || 0;
                        wishlistBadge.text(isNaN(count) ? '0' : count);

                        // حفظ العداد في ملفات تعريف الارتباط قبل الانتقال
                        setCookie('wishlistCount', count, 30);
                    }

                    // حفظ جميع العدادات قبل الانتقال
                    saveCountersToCookies();

                    // الانتقال إلى صفحة المفضلة
                    window.location.href = '@Url.Action("Index", "Wishlist")';
                },
                error: function() {
                    // حفظ العدادات حتى في حالة الخطأ
                    saveCountersToLocalStorage();

                    // في حالة حدوث خطأ، انتقل إلى الصفحة على أي حال
                    window.location.href = '@Url.Action("Index", "Wishlist")';
                }
            });
        }

        // وظيفة للانتقال إلى صفحة المقارنة بعد التأكد من تحديث العداد
        function goToCompare(event) {
            event.preventDefault();

            // تحديث عداد المقارنة أولاً
            $.ajax({
                url: '@Url.Action("GetCompareItemsCount", "Compare")',
                type: 'GET',
                success: function(data) {
                    const compareBadge = $('.compare-count');
                    if (compareBadge.length) {
                        // تحديث النص (تأكد من أن القيمة رقم وليست نصاً غير صالح)
                        const count = data.count || 0;
                        compareBadge.text(isNaN(count) ? '0' : count);

                        // حفظ العداد في ملفات تعريف الارتباط قبل الانتقال
                        setCookie('compareCount', count, 30);
                    }

                    // حفظ جميع العدادات قبل الانتقال
                    saveCountersToCookies();

                    // الانتقال إلى صفحة المقارنة
                    window.location.href = '@Url.Action("Index", "Compare")';
                },
                error: function() {
                    // حفظ العدادات حتى في حالة الخطأ
                    saveCountersToCookies();

                    // في حالة حدوث خطأ، انتقل إلى الصفحة على أي حال
                    window.location.href = '@Url.Action("Index", "Compare")';
                }
            });
        }

        // وظيفة للانتقال إلى صفحة السلة بعد التأكد من تحديث العداد
        function goToCart(event) {
            event.preventDefault();

            // تحديث عداد السلة أولاً
            $.ajax({
                url: '@Url.Action("GetCartItemsCount", "Cart")',
                type: 'GET',
                success: function(data) {
                    const cartBadge = $('.cart-count');
                    if (cartBadge.length) {
                        // تحديث النص (تأكد من أن القيمة رقم وليست نصاً غير صالح)
                        const count = data.count || 0;
                        cartBadge.text(isNaN(count) ? '0' : count);

                        // حفظ العداد في ملفات تعريف الارتباط قبل الانتقال
                        setCookie('cartCount', count, 30);
                    }

                    // حفظ جميع العدادات قبل الانتقال
                    saveCountersToCookies();

                    // الانتقال إلى صفحة السلة
                    window.location.href = '@Url.Action("Index", "Cart")';
                },
                error: function() {
                    // حفظ العدادات حتى في حالة الخطأ
                    saveCountersToCookies();

                    // في حالة حدوث خطأ، انتقل إلى الصفحة على أي حال
                    window.location.href = '@Url.Action("Index", "Cart")';
                }
            });
        }

        // وظيفة لمسح جميع ملفات تعريف الارتباط والعدادات
        function clearAllCookiesAndCounters() {
            // مسح جميع ملفات تعريف الارتباط
            const cookies = document.cookie.split(";");
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i];
                const eqPos = cookie.indexOf("=");
                const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
            }

            // مسح العدادات من ملفات تعريف الارتباط
            setCookie('wishlistCount', '0', -1); // تعيين تاريخ انتهاء سابق لحذف ملف تعريف الارتباط
            setCookie('compareCount', '0', -1);
            setCookie('cartCount', '0', -1);

            // تحديث العدادات في واجهة المستخدم
            $('.wishlist-count').text('0');
            $('.compare-count').text('0');
            $('.cart-count').text('0');

            // إرسال طلب لمسح العدادات من الخادم
            $.ajax({
                url: '@Url.Action("ClearAll", "Cookies")',
                type: 'GET',
                success: function() {
                    // عرض رسالة نجاح
                    alert('تم مسح جميع ملفات تعريف الارتباط والعدادات بنجاح');

                    // إعادة تحميل الصفحة
                    window.location.reload();
                },
                error: function() {
                    // عرض رسالة نجاح حتى في حالة الخطأ
                    alert('تم مسح العدادات المحلية بنجاح');
                }
            });
        }
    </script>

    @await RenderSectionAsync("Scripts", required: false)
    @await RenderSectionAsync("Styles", required: false)
</body>
</html>