@model IEnumerable<Abayat.Models.Category>

<ul class="navbar-nav main-nav mx-auto">
    @foreach (var category in Model.Reverse())
    {
        <li class="nav-item">
            <a class="nav-link @(ViewContext.RouteData.Values["category"]?.ToString() == category.Slug ? "active" : "")"
               asp-controller="Products" asp-action="Index" asp-route-category="@category.Slug">
                @category.Name
            </a>
        </li>
    }
    <li class="nav-item">
        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Products" && ViewContext.RouteData.Values["category"] == null ? "active" : "")"
           asp-controller="Products" asp-action="Index">
            المتجر
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")"
           asp-controller="Home" asp-action="Index">
            الرئيسية
        </a>
    </li>
</ul>
