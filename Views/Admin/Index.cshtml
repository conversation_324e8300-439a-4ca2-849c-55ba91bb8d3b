@model Abayat.ViewModels.AdminDashboardViewModel
@{
    ViewData["Title"] = "لوحة التحكم";
}

<div class="admin-dashboard">
    <div class="container py-5">
        <h1 class="admin-dashboard-title mb-4">لوحة تحكم المدير</h1>
        
        <!-- بطاقات الإحصائيات -->
        <div class="row mb-5">
            <div class="col-md-4 col-lg-2 mb-4">
                <div class="stats-card bg-primary text-white">
                    <div class="stats-card-body">
                        <div class="stats-card-icon">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <div class="stats-card-content">
                            <h5 class="stats-card-title">المنتجات</h5>
                            <h2 class="stats-card-value">@Model.TotalProducts</h2>
                        </div>
                    </div>
                    <div class="stats-card-footer">
                        <a asp-controller="Products" asp-action="Index" class="text-white">
                            <span>عرض الكل</span>
                            <i class="bi bi-arrow-left"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 col-lg-2 mb-4">
                <div class="stats-card bg-success text-white">
                    <div class="stats-card-body">
                        <div class="stats-card-icon">
                            <i class="bi bi-tags"></i>
                        </div>
                        <div class="stats-card-content">
                            <h5 class="stats-card-title">الفئات</h5>
                            <h2 class="stats-card-value">@Model.TotalCategories</h2>
                        </div>
                    </div>
                    <div class="stats-card-footer">
                        <a asp-controller="Categories" asp-action="Index" class="text-white">
                            <span>عرض الكل</span>
                            <i class="bi bi-arrow-left"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 col-lg-2 mb-4">
                <div class="stats-card bg-info text-white">
                    <div class="stats-card-body">
                        <div class="stats-card-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stats-card-content">
                            <h5 class="stats-card-title">المستخدمين</h5>
                            <h2 class="stats-card-value">@Model.TotalUsers</h2>
                        </div>
                    </div>
                    <div class="stats-card-footer">
                        <a asp-controller="Users" asp-action="Index" class="text-white">
                            <span>عرض الكل</span>
                            <i class="bi bi-arrow-left"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 col-lg-2 mb-4">
                <div class="stats-card bg-warning text-white">
                    <div class="stats-card-body">
                        <div class="stats-card-icon">
                            <i class="bi bi-bag"></i>
                        </div>
                        <div class="stats-card-content">
                            <h5 class="stats-card-title">الطلبات</h5>
                            <h2 class="stats-card-value">@Model.TotalOrders</h2>
                        </div>
                    </div>
                    <div class="stats-card-footer">
                        <a asp-controller="Orders" asp-action="Manage" class="text-white">
                            <span>عرض الكل</span>
                            <i class="bi bi-arrow-left"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 col-lg-2 mb-4">
                <div class="stats-card bg-danger text-white">
                    <div class="stats-card-body">
                        <div class="stats-card-icon">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="stats-card-content">
                            <h5 class="stats-card-title">المبيعات</h5>
                            <h2 class="stats-card-value">@Model.TotalSales.ToString("N0") ر.ع</h2>
                        </div>
                    </div>
                    <div class="stats-card-footer">
                        <a asp-controller="Orders" asp-action="Manage" class="text-white">
                            <span>عرض التفاصيل</span>
                            <i class="bi bi-arrow-left"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 col-lg-2 mb-4">
                <div class="stats-card bg-purple text-white">
                    <div class="stats-card-body">
                        <div class="stats-card-icon">
                            <i class="bi bi-images"></i>
                        </div>
                        <div class="stats-card-content">
                            <h5 class="stats-card-title">الشريط المتحرك</h5>
                            <h2 class="stats-card-value"><i class="bi bi-pencil-square"></i></h2>
                        </div>
                    </div>
                    <div class="stats-card-footer">
                        <a asp-controller="CarouselImages" asp-action="Index" class="text-white">
                            <span>إدارة الصور</span>
                            <i class="bi bi-arrow-left"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- حالة الطلبات -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h4 class="admin-card-title">حالة الطلبات</h4>
                    </div>
                    <div class="admin-card-body">
                        <div class="row">
                            <div class="col-md-4 col-lg-2 mb-3">
                                <div class="order-status-card">
                                    <div class="order-status-icon pending">
                                        <i class="bi bi-hourglass-split"></i>
                                    </div>
                                    <div class="order-status-details">
                                        <h5>قيد الانتظار</h5>
                                        <h3>@Model.PendingOrders</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-2 mb-3">
                                <div class="order-status-card">
                                    <div class="order-status-icon confirmed">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                    <div class="order-status-details">
                                        <h5>مؤكدة</h5>
                                        <h3>@Model.ConfirmedOrders</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-2 mb-3">
                                <div class="order-status-card">
                                    <div class="order-status-icon processing">
                                        <i class="bi bi-gear"></i>
                                    </div>
                                    <div class="order-status-details">
                                        <h5>قيد المعالجة</h5>
                                        <h3>@Model.ProcessingOrders</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-2 mb-3">
                                <div class="order-status-card">
                                    <div class="order-status-icon shipped">
                                        <i class="bi bi-truck"></i>
                                    </div>
                                    <div class="order-status-details">
                                        <h5>تم الشحن</h5>
                                        <h3>@Model.ShippedOrders</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-2 mb-3">
                                <div class="order-status-card">
                                    <div class="order-status-icon delivered">
                                        <i class="bi bi-check2-all"></i>
                                    </div>
                                    <div class="order-status-details">
                                        <h5>تم التسليم</h5>
                                        <h3>@Model.DeliveredOrders</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-2 mb-3">
                                <div class="order-status-card">
                                    <div class="order-status-icon cancelled">
                                        <i class="bi bi-x-circle"></i>
                                    </div>
                                    <div class="order-status-details">
                                        <h5>ملغية</h5>
                                        <h3>@Model.CancelledOrders</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- آخر الطلبات والمنتجات الأكثر مبيعًا -->
        <div class="row mb-5">
            <div class="col-lg-6 mb-4">
                <div class="admin-card h-100">
                    <div class="admin-card-header d-flex justify-content-between align-items-center">
                        <h4 class="admin-card-title">آخر الطلبات</h4>
                        <a asp-controller="Orders" asp-action="Manage" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="admin-card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var order in Model.RecentOrders)
                                    {
                                        <tr>
                                            <td>#@order.Id</td>
                                            <td>@order.User?.Name</td>
                                            <td>@order.OrderDate.ToString("yyyy/MM/dd")</td>
                                            <td>@order.TotalAmount.ToString("N0") ر.ع</td>
                                            <td>
                                                <span class="badge @GetStatusBadgeClass(order.Status)">
                                                    @GetStatusDisplayName(order.Status)
                                                </span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="admin-card h-100">
                    <div class="admin-card-header d-flex justify-content-between align-items-center">
                        <h4 class="admin-card-title">المنتجات الأكثر مبيعًا</h4>
                        <a asp-controller="Products" asp-action="Index" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="admin-card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية المباعة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var product in Model.TopSellingProducts)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="product-img-small me-2">
                                                        @if (!string.IsNullOrEmpty(product.ProductImageUrl))
                                                        {
                                                            <img src="@product.ProductImageUrl" alt="@product.ProductName" />
                                                        }
                                                        else
                                                        {
                                                            <img src="https://placehold.co/100x100/6a0dad/ffffff?text=@product.ProductName.Substring(0, 1)" alt="@product.ProductName" />
                                                        }
                                                    </div>
                                                    <span>@product.ProductName</span>
                                                </div>
                                            </td>
                                            <td>@product.ProductPrice.ToString("N0") ر.ع</td>
                                            <td>@product.TotalQuantity</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetStatusBadgeClass(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bg-warning",
            OrderStatus.Confirmed => "bg-info",
            OrderStatus.Processing => "bg-primary",
            OrderStatus.Shipped => "bg-secondary",
            OrderStatus.Delivered => "bg-success",
            OrderStatus.Cancelled => "bg-danger",
            _ => "bg-light"
        };
    }

    string GetStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "قيد الانتظار",
            OrderStatus.Confirmed => "مؤكد",
            OrderStatus.Processing => "قيد المعالجة",
            OrderStatus.Shipped => "تم الشحن",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "ملغي",
            _ => "غير معروف"
        };
    }
}
