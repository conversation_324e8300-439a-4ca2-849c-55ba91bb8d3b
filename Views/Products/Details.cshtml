@model Abayat.Models.Product

@{
    ViewData["Title"] = "تفاصيل المنتج";
}

@* Token antifalsificación para solicitudes AJAX *@
@Html.AntiForgeryToken()

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@Model.Name</h1>
        <nav aria-label="breadcrumb" class="mb-4 d-flex justify-content-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item"><a asp-controller="Products" asp-action="Index" class="text-white">المنتجات</a></li>
                <li class="breadcrumb-item active text-white" aria-current="page">@Model.Name</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container py-5">
    <div class="product-details slide-in-right">
        <div class="row g-0">
            <div class="col-lg-6">
                <div class="product-gallery position-relative overflow-hidden">
                    <div class="product-main-image zoom-effect">
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <img src="@Model.ImageUrl" class="product-details-img" alt="@Model.Name">
                        }
                        else
                        {
                            <img src="https://placehold.co/800x600/6a0dad/ffffff?text=@Model.Name" class="product-details-img" alt="@Model.Name">
                        }
                    </div>

                    <span class="product-badge @(Model.IsAvailable ? "bg-success" : "bg-danger")">
                        @(Model.IsAvailable ? "متوفر" : "غير متوفر")
                    </span>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="product-details-content">
                    <span class="product-category product-category-@(Model.Category?.Slug ?? "default") mb-3 d-inline-block">@(Model.ProductType ?? Model.Category?.Name)</span>
                    <h1 class="product-details-title">@Model.Name</h1>

                    <div class="product-details-price"><span class="currency">ر.ع</span> @Model.Price.ToString("N0")</div>

                    <div class="product-details-description">
                        @Model.Description
                    </div>

                    <div class="d-flex align-items-center mb-4">
                        <i class="bi bi-calendar3 text-muted me-2"></i>
                        <span class="text-muted">تاريخ الإضافة: @Model.CreatedAt.ToString("dd/MM/yyyy")</span>
                    </div>

                    <!-- أزرار المقارنة والمفضلة -->
                    <div class="product-action-buttons d-flex gap-2 mb-4">
                        <button type="button" class="btn btn-outline-primary add-to-compare-btn product-item-action-btn" data-product-id="@Model.Id">
                            <i class="bi bi-arrow-left-right"></i> <span>مقارنة</span>
                        </button>
                        <button type="button" class="btn btn-outline-primary add-to-wishlist-btn product-item-action-btn" data-product-id="@Model.Id">
                            <i class="bi bi-heart"></i> <span>المفضلة</span>
                        </button>
                    </div>

                    <div class="product-details-actions">
                        @if (TempData["SuccessMessage"] != null)
                        {
                            <div class="alert alert-success alert-dismissible fade show mb-3" role="alert">
                                <i class="bi bi-check-circle me-2"></i> @TempData["SuccessMessage"]
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        }

                        @if (TempData["ErrorMessage"] != null)
                        {
                            <div class="alert alert-danger alert-dismissible fade show mb-3" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        }

                        @if (!Model.IsAvailable && !User.IsInRole("Admin"))
                        {
                            <div class="alert alert-danger mb-3">
                                <i class="bi bi-exclamation-triangle me-2"></i> هذا المنتج غير متوفر حالياً
                            </div>
                        }
                        else if (Model.IsAvailable && !User.IsInRole("Admin"))
                        {
                            <form asp-controller="Cart" asp-action="AddToCart" method="post" class="mb-3">
                                <input type="hidden" name="productId" value="@Model.Id" />
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <label class="me-2 fw-bold">الكمية:</label>
                                    <div class="quantity-wrapper d-flex align-items-center">
                                        <button type="button" class="btn btn-outline-primary rounded-circle quantity-btn" data-action="decrease">
                                            <i class="bi bi-dash"></i>
                                        </button>
                                        <input type="number" name="quantity" value="1" min="1" max="100" class="form-control text-center quantity-input mx-2" style="width: 60px;" readonly />
                                        <button type="button" class="btn btn-outline-primary rounded-circle quantity-btn" data-action="increase">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary product-details-btn w-100">
                                    <i class="bi bi-cart-plus me-2"></i> إضافة إلى السلة
                                </button>
                            </form>
                        }

                        @if (User.IsInRole("Admin"))
                        {
                            <div class="admin-actions mt-3">
                                <h5 class="admin-section-title mb-3">خيارات الإدارة</h5>
                                <div class="d-flex gap-2">
                                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary product-details-btn flex-grow-1">
                                        <i class="bi bi-pencil me-1"></i> تعديل
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger product-details-btn flex-grow-1">
                                        <i class="bi bi-trash me-1"></i> حذف
                                    </a>
                                </div>
                                <div class="mt-2">
                                    <a asp-action="Index" asp-controller="Products" class="btn btn-outline-secondary product-details-btn w-100">
                                        <i class="bi bi-gear me-1"></i> إدارة المنتجات
                                    </a>
                                </div>
                                <div class="mt-2">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="showInCarouselSwitch" disabled>
                                        <label class="form-check-label" for="showInCarouselSwitch">
                                            عرض في الشريط المتحرك
                                        </label>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="isAvailableSwitch" @(Model.IsAvailable ? "checked" : "")>
                                        <label class="form-check-label" for="isAvailableSwitch">
                                            متوفر للبيع
                                        </label>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-5">
        <a asp-action="Index" class="btn btn-outline-primary product-btn">
            <i class="bi bi-arrow-right me-1"></i> العودة إلى قائمة المنتجات
        </a>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // زيادة ونقصان الكمية
            $('.quantity-btn').on('click', function () {
                var action = $(this).data('action');
                var input = $(this).closest('.quantity-wrapper').find('.quantity-input');
                var currentValue = parseInt(input.val());

                if (action === 'increase') {
                    if (currentValue < 100) {
                        input.val(currentValue + 1);
                    }
                } else if (action === 'decrease') {
                    if (currentValue > 1) {
                        input.val(currentValue - 1);
                    }
                }
            });

            // تبديل حالة توفر المنتج
            $('#isAvailableSwitch').on('change', function() {
                var isChecked = $(this).prop('checked');
                var productId = @Model.Id;

                // إظهار رسالة تأكيد
                if (!confirm(isChecked ? 'هل أنت متأكد من تعيين المنتج كمتوفر للبيع؟' : 'هل أنت متأكد من تعيين المنتج كغير متوفر للبيع؟')) {
                    // إعادة الزر إلى حالته السابقة إذا تم إلغاء التأكيد
                    $(this).prop('checked', !isChecked);
                    return;
                }

                // إرسال طلب AJAX لتبديل حالة توفر المنتج
                $.ajax({
                    url: '/Products/ToggleAvailability/' + productId,
                    type: 'POST',
                    data: {
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // عرض رسالة نجاح
                            alert(response.message);

                            // تحديث الصفحة لتحديث واجهة المستخدم
                            window.location.href = window.location.href;
                        } else {
                            // عرض رسالة خطأ
                            alert('حدث خطأ أثناء تحديث المنتج');
                            // إعادة الزر إلى حالته السابقة
                            $('#isAvailableSwitch').prop('checked', !isChecked);
                        }
                    },
                    error: function() {
                        // عرض رسالة خطأ
                        alert('حدث خطأ أثناء تحديث المنتج');
                        // إعادة الزر إلى حالته السابقة
                        $('#isAvailableSwitch').prop('checked', !isChecked);
                    }
                });
            });
        });
    </script>
}
