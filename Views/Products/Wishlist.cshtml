@{
    ViewData["Title"] = "المفضلة";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">المنتجات المفضلة</h1>
        <p class="lead mb-4">قائمة المنتجات التي أضفتها إلى المفضلة</p>
        <nav aria-label="breadcrumb" class="mb-4 d-flex justify-content-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item"><a asp-controller="Products" asp-action="Index" class="text-white">المنتجات</a></li>
                <li class="breadcrumb-item active text-white" aria-current="page">المفضلة</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container py-5">
    <div class="wishlist-page-header">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="wishlist-title">المنتجات المفضلة</h2>
            <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary">
                <i class="bi bi-arrow-right me-1"></i> العودة إلى المنتجات
            </a>
        </div>
        <p class="wishlist-subtitle">قائمة المنتجات التي أضفتها إلى المفضلة</p>
    </div>

    <div id="wishlist-container">
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-1"></i> العودة إلى المنتجات
        </a>
    </div>
</div>

<style>
    /* تنسيق صفحة المفضلة */
    .wishlist-page-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .wishlist-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .wishlist-subtitle {
        color: var(--text-muted);
        font-size: 1rem;
    }

    /* رسالة عدم وجود منتجات */
    .alert-info {
        background-color: rgba(106, 13, 173, 0.05);
        border: 1px solid rgba(106, 13, 173, 0.1);
        color: var(--dark-color);
        border-radius: 8px;
        padding: 2rem;
    }

    .alert-heading {
        color: var(--primary-color);
        font-weight: 600;
    }

    .fade-out {
        animation: fadeOut 0.3s forwards;
    }

    @@keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
</style>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            loadWishlistProducts();
        });

        function loadWishlistProducts() {
            const wishlistContainer = document.getElementById('wishlist-container');
            const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

            if (wishlist.length === 0) {
                wishlistContainer.innerHTML = `
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-info-circle-fill fs-3 me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">لا توجد منتجات في المفضلة</h5>
                                <p class="mb-0">لم تقم بإضافة أي منتجات إلى المفضلة بعد.</p>
                                <a href="/Products" class="btn btn-primary mt-3">تصفح المنتجات</a>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            // Obtener productos por IDs
            fetch('/Products/GetProductsByIds?ids=' + wishlist.join(','))
                .then(response => response.json())
                .then(products => {
                    if (products.length === 0) {
                        wishlistContainer.innerHTML = `
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center justify-content-center">
                                    <i class="bi bi-info-circle-fill fs-3 me-3"></i>
                                    <div>
                                        <h5 class="alert-heading mb-1">لا توجد منتجات في المفضلة</h5>
                                        <p class="mb-0">لم تقم بإضافة أي منتجات إلى المفضلة بعد.</p>
                                        <a href="/Products" class="btn btn-primary mt-3">تصفح المنتجات</a>
                                    </div>
                                </div>
                            </div>
                        `;
                        return;
                    }

                    // Crear grid de productos
                    let html = `
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3>المنتجات المفضلة (<span>${products.length}</span>)</h3>
                            <button class="btn btn-outline-danger" onclick="clearWishlist()">
                                <i class="bi bi-trash me-1"></i> حذف الكل
                            </button>
                        </div>
                        <div class="product-grid">
                    `;

                    // Añadir productos
                    products.forEach(product => {
                        html += `
                            <div class="product-card" data-product-id="${product.id}">
                                <div class="product-img-container">
                                    <img src="${product.imageUrl || `https://placehold.co/600x400/6a0dad/ffffff?text=${product.name}`}" class="product-img" alt="${product.name}">

                                    <button class="wishlist-btn active" data-product-id="${product.id}" onclick="removeFromWishlist(${product.id}, this)">
                                        <i class="bi bi-heart-fill"></i>
                                    </button>

                                    @* Discount percentage is not implemented yet *@

                                    <div class="product-actions-menu">
                                        <button class="product-action-btn add-to-cart-btn" data-product-id="${product.id}" title="إضافة للسلة" onclick="addToCart(${product.id})">
                                            <i class="bi bi-cart-plus"></i>
                                        </button>
                                        <button class="product-action-btn add-to-compare-btn" data-product-id="${product.id}" title="إضافة للمقارنة" onclick="addToCompare(${product.id}, this)">
                                            <i class="bi bi-arrow-left-right"></i>
                                        </button>
                                        <a href="/Products/Details/${product.id}" class="product-action-btn view-details-btn" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="product-content">
                                    <div class="product-category">${product.productType || product.category?.name || ''}</div>
                                    <h3 class="product-title">${product.name}</h3>
                                    <div class="product-price">${product.price.toLocaleString()} <span class="currency">ر.ع</span></div>
                                </div>
                            </div>
                        `;
                    });

                    html += `</div>`;
                    wishlistContainer.innerHTML = html;

                    // Inicializar botones de comparación
                    initCompareButtons();
                })
                .catch(error => {
                    console.error('Error al cargar productos:', error);
                    wishlistContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="bi bi-exclamation-triangle-fill fs-3 me-3"></i>
                                <div>
                                    <h5 class="alert-heading mb-1">حدث خطأ</h5>
                                    <p class="mb-0">لم نتمكن من تحميل المنتجات المفضلة. يرجى المحاولة مرة أخرى.</p>
                                    <button class="btn btn-primary mt-3" onclick="loadWishlistProducts()">إعادة المحاولة</button>
                                </div>
                            </div>
                        </div>
                    `;
                });
        }

        function removeFromWishlist(productId, button) {
            // Obtener lista actual
            let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

            // Eliminar producto
            wishlist = wishlist.filter(id => id != productId);

            // Guardar lista actualizada
            localStorage.setItem('wishlist', JSON.stringify(wishlist));

            // إطلاق حدث تحديث المفضلة
            $(document).trigger('wishlist:updated');

            // Eliminar tarjeta de producto
            const productCard = button.closest('.product-card');
            productCard.classList.add('fade-out');

            setTimeout(() => {
                productCard.remove();

                // Actualizar contador
                const countElement = document.querySelector('h3 span');
                if (countElement) {
                    countElement.textContent = wishlist.length;
                }

                // Mostrar mensaje si no hay productos
                if (wishlist.length === 0) {
                    loadWishlistProducts();
                }
            }, 300);

            showToast('تمت إزالة المنتج من المفضلة');
        }

        function clearWishlist() {
            if (confirm('هل أنت متأكد من حذف جميع المنتجات من المفضلة؟')) {
                localStorage.setItem('wishlist', JSON.stringify([]));
                loadWishlistProducts();
                showToast('تم حذف جميع المنتجات من المفضلة');

                // إطلاق حدث تحديث المفضلة
                $(document).trigger('wishlist:updated');
            }
        }

        function addToCompare(productId, button) {
            // الحصول على قائمة المقارنة الحالية
            let compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            // التحقق مما إذا كان المنتج موجودًا بالفعل في المقارنة
            if (compareList.includes(productId.toString())) {
                // إزالة المنتج من المقارنة
                compareList = compareList.filter(id => id != productId);
                button.classList.remove('active');
                showToast('تمت إزالة المنتج من المقارنة');
            } else {
                // التحقق من عدد المنتجات في المقارنة (الحد الأقصى 4)
                if (compareList.length >= 4) {
                    // إزالة أول منتج من القائمة
                    compareList.shift();
                }

                // إضافة المنتج إلى المقارنة
                compareList.push(productId.toString());
                button.classList.add('active');
                showToast('تمت إضافة المنتج إلى المقارنة');
            }

            // حفظ القائمة المحدثة
            localStorage.setItem('compareList', JSON.stringify(compareList));

            // إطلاق حدث تحديث المقارنة
            $(document).trigger('compare:updated');
        }

        function initCompareButtons() {
            const compareButtons = document.querySelectorAll('.add-to-compare-btn');
            const compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            compareButtons.forEach(button => {
                const productId = button.getAttribute('data-product-id');
                if (compareList.includes(productId)) {
                    button.classList.add('active');
                }
            });
        }

        function addToCart(productId) {
            // Enviar solicitud AJAX para añadir al carrito
            $.ajax({
                url: '/Cart/AddToCart',
                type: 'POST',
                data: {
                    productId: productId,
                    quantity: 1,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showToast('تمت إضافة المنتج إلى السلة');
                        // Actualizar contador del carrito
                        updateCartCount();
                    } else {
                        showToast('حدث خطأ أثناء إضافة المنتج');
                    }
                },
                error: function() {
                    showToast('حدث خطأ أثناء إضافة المنتج');
                }
            });
        }

        function updateCartCount() {
            $.ajax({
                url: '/Cart/GetCartItemsCount',
                type: 'GET',
                success: function(response) {
                    const cartBadge = document.querySelector('.cart-count');
                    if (cartBadge) {
                        cartBadge.textContent = response.count;

                        // Animar el contador
                        cartBadge.classList.add('pulse');
                        setTimeout(() => {
                            cartBadge.classList.remove('pulse');
                        }, 1000);
                    }
                }
            });
        }

        function showToast(message) {
            // Crear elemento toast si no existe
            let toast = document.getElementById('toast-notification');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-notification';
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.right = '20px';
                toast.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '12px 20px';
                toast.style.borderRadius = '4px';
                toast.style.zIndex = '1000';
                toast.style.transition = 'opacity 0.5s ease-in-out';
                toast.style.opacity = '0';
                toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                document.body.appendChild(toast);
            }

            // Mostrar mensaje
            toast.textContent = message;
            toast.style.opacity = '1';

            // Ocultar después de 3 segundos
            setTimeout(() => {
                toast.style.opacity = '0';
            }, 3000);
        }
    </script>
}
