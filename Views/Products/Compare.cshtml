@{
    ViewData["Title"] = "مقارنة المنتجات";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">مقارنة المنتجات</h1>
        <p class="lead mb-4">قارن بين المنتجات لاختيار الأفضل لك</p>
        <nav aria-label="breadcrumb" class="mb-4 d-flex justify-content-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item"><a asp-controller="Products" asp-action="Index" class="text-white">المنتجات</a></li>
                <li class="breadcrumb-item active text-white" aria-current="page">المقارنة</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container py-5">
    <div class="compare-page-header">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="compare-title">مقارنة المنتجات</h2>
            <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary">
                <i class="bi bi-arrow-right me-1"></i> العودة إلى المنتجات
            </a>
        </div>
        <p class="compare-subtitle">قارن بين المنتجات لاختيار الأفضل لك</p>
    </div>

    <div id="compare-container">
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-1"></i> العودة إلى المنتجات
        </a>
    </div>
</div>

<style>
    /* تنسيق صفحة المقارنة */
    .compare-page-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .compare-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .compare-subtitle {
        color: var(--text-muted);
        font-size: 1rem;
    }

    .compare-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 2rem;
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .compare-table th {
        background-color: rgba(106, 13, 173, 0.05);
        padding: 1rem;
        text-align: right;
        font-weight: 600;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .compare-table td {
        padding: 1rem;
        text-align: center;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .compare-table tr:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.01);
    }

    .compare-product-img {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 4px;
        margin: 0 auto;
        display: block;
        transition: transform 0.3s ease;
    }

    .compare-product-img:hover {
        transform: scale(1.05);
    }

    .compare-product-title {
        font-weight: 600;
        margin: 1rem 0;
        font-size: 1rem;
        color: var(--primary-color);
    }

    .compare-product-price {
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .compare-product-price .currency {
        font-size: 0.8rem;
        margin-right: 0.25rem;
    }

    .compare-product-actions {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .compare-product-actions .btn {
        padding: 0.4rem 0.75rem;
        font-size: 0.85rem;
    }

    .compare-remove-btn {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: rgba(255, 0, 0, 0.1);
        color: #ff0000;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.3s ease;
    }

    .compare-remove-btn:hover {
        background-color: rgba(255, 0, 0, 0.2);
        transform: scale(1.1);
    }

    .compare-product-cell {
        position: relative;
        min-width: 200px;
    }

    .fade-out {
        animation: fadeOut 0.3s forwards;
    }

    @@keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }

    /* رسالة عدم وجود منتجات */
    .alert-info {
        background-color: rgba(106, 13, 173, 0.05);
        border: 1px solid rgba(106, 13, 173, 0.1);
        color: var(--dark-color);
        border-radius: 8px;
        padding: 2rem;
    }

    .alert-warning {
        background-color: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.2);
        color: var(--dark-color);
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
    }

    .alert-heading {
        color: var(--primary-color);
        font-weight: 600;
    }

    .compare-products-count {
        font-size: 1.4rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .compare-products-count span {
        color: var(--danger);
        font-weight: 700;
    }
</style>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            loadCompareProducts();
            // تم إزالة استدعاء وظائف غير معرفة
        });

        function loadCompareProducts() {
            const compareContainer = document.getElementById('compare-container');
            const compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            if (compareList.length === 0) {
                compareContainer.innerHTML = `
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-info-circle-fill fs-3 me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">لا توجد منتجات للمقارنة</h5>
                                <p class="mb-0">لم تقم بإضافة أي منتجات للمقارنة بعد.</p>
                                <a href="/Products" class="btn btn-primary mt-3">تصفح المنتجات</a>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            // Obtener productos por IDs
            fetch('/Products/GetProductsByIds?ids=' + compareList.join(','))
                .then(response => response.json())
                .then(products => {
                    if (products.length === 0) {
                        compareContainer.innerHTML = `
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center justify-content-center">
                                    <i class="bi bi-info-circle-fill fs-3 me-3"></i>
                                    <div>
                                        <h5 class="alert-heading mb-1">لا توجد منتجات للمقارنة</h5>
                                        <p class="mb-0">لم تقم بإضافة أي منتجات للمقارنة بعد.</p>
                                        <a href="/Products" class="btn btn-primary mt-3">تصفح المنتجات</a>
                                    </div>
                                </div>
                            </div>
                        `;
                        return;
                    }

                    // Crear tabla de comparación
                    let html = `
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3 class="compare-products-count">مقارنة المنتجات (<span>${products.length}</span>)</h3>
                            <button class="btn btn-outline-danger" onclick="clearCompare()">
                                <i class="bi bi-trash me-1"></i> حذف الكل
                            </button>
                        </div>
                        <div class="alert alert-warning mb-4">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            قارن بين المنتجات لاختيار الأفضل لك
                        </div>
                        <div class="table-responsive">
                            <table class="compare-table">
                                <tr>
                                    <th>المنتج</th>
                    `;

                    // Añadir encabezados de productos
                    products.forEach(product => {
                        html += `
                            <td class="compare-product-cell" data-product-id="${product.id}">
                                <button class="compare-remove-btn" onclick="removeFromCompare(${product.id}, this)">
                                    <i class="bi bi-x"></i>
                                </button>
                                <img src="${product.imageUrl || `https://placehold.co/600x400/6a0dad/ffffff?text=${product.name}`}" class="compare-product-img" alt="${product.name}">
                                <h4 class="compare-product-title">${product.name}</h4>
                                <div class="compare-product-price">${product.price.toLocaleString()} <span class="currency">ر.ع</span></div>
                                <div class="compare-product-actions">
                                    <button class="btn btn-sm btn-primary" onclick="addToCart(${product.id})">
                                        <i class="bi bi-cart-plus me-1"></i> إضافة للسلة
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger add-to-wishlist-btn" data-product-id="${product.id}" onclick="addToWishlist(${product.id}, this)">
                                        <i class="bi bi-heart me-1"></i>
                                    </button>
                                </div>
                            </td>
                        `;
                    });

                    html += `</tr>`;

                    // Añadir filas de comparación
                    const attributes = [
                        { name: 'الفئة', key: 'category.name' },
                        { name: 'النوع', key: 'productType' },
                        { name: 'الوصف', key: 'description' },
                        { name: 'متوفر', key: 'isAvailable', type: 'boolean' }
                    ];

                    attributes.forEach(attr => {
                        html += `<tr><th>${attr.name}</th>`;

                        products.forEach(product => {
                            let value = '';

                            if (attr.key.includes('.')) {
                                const keys = attr.key.split('.');
                                let obj = product;
                                for (const key of keys) {
                                    obj = obj?.[key];
                                    if (obj === undefined) break;
                                }
                                value = obj || '-';
                            } else {
                                value = product[attr.key] !== undefined ? product[attr.key] : '-';
                            }

                            if (attr.type === 'boolean') {
                                value = value === true ? '<i class="bi bi-check-circle-fill text-success"></i>' :
                                       value === false ? '<i class="bi bi-x-circle-fill text-danger"></i>' : '-';
                            }

                            html += `<td>${value}</td>`;
                        });

                        html += `</tr>`;
                    });

                    html += `</table></div>`;
                    compareContainer.innerHTML = html;

                    // Inicializar botones de favoritos
                    initWishlistButtons();
                })
                .catch(error => {
                    console.error('Error al cargar productos:', error);
                    compareContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="bi bi-exclamation-triangle-fill fs-3 me-3"></i>
                                <div>
                                    <h5 class="alert-heading mb-1">حدث خطأ</h5>
                                    <p class="mb-0">لم نتمكن من تحميل منتجات المقارنة. يرجى المحاولة مرة أخرى.</p>
                                    <button class="btn btn-primary mt-3" onclick="loadCompareProducts()">إعادة المحاولة</button>
                                </div>
                            </div>
                        </div>
                    `;
                });
        }

        function removeFromCompare(productId, button) {
            // Obtener lista actual
            let compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            // Eliminar producto
            compareList = compareList.filter(id => id != productId);

            // Guardar lista actualizada
            localStorage.setItem('compareList', JSON.stringify(compareList));

            // إطلاق حدث تحديث المقارنة
            $(document).trigger('compare:updated');

            // Eliminar columna de producto
            const productCell = button.closest('.compare-product-cell');
            const table = productCell.closest('table');
            const columnIndex = Array.from(productCell.parentNode.children).indexOf(productCell);

            // Aplicar efecto de desvanecimiento
            const cells = table.querySelectorAll(`tr td:nth-child(${columnIndex + 1})`);
            cells.forEach(cell => cell.classList.add('fade-out'));

            setTimeout(() => {
                // Si solo queda un producto, recargar toda la vista
                if (compareList.length <= 1) {
                    loadCompareProducts();
                    return;
                }

                // Eliminar columna
                cells.forEach(cell => cell.remove());

                // Actualizar contador
                const countElement = document.querySelector('h3 span');
                if (countElement) {
                    countElement.textContent = compareList.length;
                }
            }, 300);

            showToast('تمت إزالة المنتج من المقارنة');
        }

        function clearCompare() {
            if (confirm('هل أنت متأكد من حذف جميع المنتجات من المقارنة؟')) {
                localStorage.setItem('compareList', JSON.stringify([]));
                loadCompareProducts();
                showToast('تم حذف جميع المنتجات من المقارنة');

                // إطلاق حدث تحديث المقارنة
                $(document).trigger('compare:updated');
            }
        }

        function addToCart(productId) {
            // Enviar solicitud AJAX para añadir al carrito
            $.ajax({
                url: '/Cart/AddToCart',
                type: 'POST',
                data: {
                    productId: productId,
                    quantity: 1,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showToast('تمت إضافة المنتج إلى السلة');
                        // Actualizar contador del carrito
                        updateCartCount();
                    } else {
                        showToast('حدث خطأ أثناء إضافة المنتج');
                    }
                },
                error: function() {
                    showToast('حدث خطأ أثناء إضافة المنتج');
                }
            });
        }

        function addToWishlist(productId, button) {
            // Obtener lista actual
            let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

            // Alternar estado
            if (wishlist.includes(productId.toString())) {
                wishlist = wishlist.filter(id => id != productId);
                button.classList.remove('active');
                showToast('تمت إزالة المنتج من المفضلة');
            } else {
                wishlist.push(productId.toString());
                button.classList.add('active');
                showToast('تمت إضافة المنتج إلى المفضلة');
            }

            // Guardar lista actualizada
            localStorage.setItem('wishlist', JSON.stringify(wishlist));
        }

        function initWishlistButtons() {
            const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');
            const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

            wishlistButtons.forEach(button => {
                const productId = button.getAttribute('data-product-id');
                if (wishlist.includes(productId)) {
                    button.classList.add('active');
                }
            });
        }

        function updateCartCount() {
            $.ajax({
                url: '/Cart/GetCartItemsCount',
                type: 'GET',
                success: function(response) {
                    const cartBadge = document.querySelector('.cart-count');
                    if (cartBadge) {
                        cartBadge.textContent = response.count;

                        // Animar el contador
                        cartBadge.classList.add('pulse');
                        setTimeout(() => {
                            cartBadge.classList.remove('pulse');
                        }, 1000);
                    }
                }
            });
        }

        function showToast(message) {
            // Crear elemento toast si no existe
            let toast = document.getElementById('toast-notification');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-notification';
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.right = '20px';
                toast.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '12px 20px';
                toast.style.borderRadius = '4px';
                toast.style.zIndex = '1000';
                toast.style.transition = 'opacity 0.5s ease-in-out';
                toast.style.opacity = '0';
                toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                document.body.appendChild(toast);
            }

            // Mostrar mensaje
            toast.textContent = message;
            toast.style.opacity = '1';

            // Ocultar después de 3 segundos
            setTimeout(() => {
                toast.style.opacity = '0';
            }, 3000);
        }
    </script>
}
