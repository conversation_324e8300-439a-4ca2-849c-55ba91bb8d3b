@model Abayat.Models.Product

@{
    ViewData["Title"] = "إضافة منتج جديد";
    var categories = ViewData["Categories"] as SelectList;
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">قم بإضافة منتج جديد لعرضه في المتجر</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card zoom-in">
                <h2 class="form-title">بيانات المنتج</h2>

                <form asp-action="Create" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Name" class="form-label">اسم المنتج</label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم المنتج" />
                                <span asp-validation-for="Name" class="field-validation-error"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Price" class="form-label">السعر</label>
                                <div class="input-group">
                                    <span class="input-group-text">ر.ع</span>
                                    <input asp-for="Price" class="form-control" placeholder="0.00" />
                                </div>
                                <span asp-validation-for="Price" class="field-validation-error"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label asp-for="Description" class="form-label">وصف المنتج</label>
                        <textarea asp-for="Description" class="form-control" rows="4" placeholder="أدخل وصف المنتج بالتفصيل"></textarea>
                        <span asp-validation-for="Description" class="field-validation-error"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="CategoryId" class="form-label">الفئة</label>
                                <select asp-for="CategoryId" asp-items="categories" class="form-select">
                                    <option value="">-- اختر الفئة --</option>
                                </select>
                                <span asp-validation-for="CategoryId" class="field-validation-error"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="imageFile" class="form-label">صورة المنتج</label>
                                <input type="file" name="imageFile" id="imageFile" class="form-control" accept="image/*" />
                                <small class="text-muted">يفضل صورة بحجم 800x600 بكسل</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ProductType" class="form-label">نوع المنتج</label>
                                <input asp-for="ProductType" class="form-control" placeholder="مثل: كاجوال، رسمي، مطرز" />
                                <span asp-validation-for="ProductType" class="field-validation-error"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mt-3">
                                <div class="form-check">
                                    <input asp-for="IsAvailable" class="form-check-input" />
                                    <label asp-for="IsAvailable" class="form-check-label">متوفر للبيع</label>
                                </div>
                                <div class="form-check mt-2">
                                    <input asp-for="ShowInCarousel" class="form-check-input" />
                                    <label asp-for="ShowInCarousel" class="form-check-label">عرض في الشريط المتحرك</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <a asp-action="Index" class="btn btn-outline-secondary form-btn">
                            <i class="bi bi-arrow-right"></i> العودة إلى القائمة
                        </a>
                        <button type="submit" class="btn btn-primary form-btn">
                            <i class="bi bi-check-circle"></i> إضافة المنتج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
