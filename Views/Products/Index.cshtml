@model Abayat.Models.PaginatedList<Abayat.Models.Product>

@{
    var pageSize = ViewData["PageSize"] as int? ?? 16;
    var selectedCategory = ViewData["SelectedCategory"] as string;
    var categories = ViewData["Categories"] as List<Abayat.Models.Category>;

    if (!string.IsNullOrEmpty(selectedCategory) && categories != null)
    {
        var category = categories.FirstOrDefault(c => c.Slug == selectedCategory);
        if (category != null)
        {
            ViewData["Title"] = "منتجات " + category.Name;
        }
        else
        {
            ViewData["Title"] = "جميع المنتجات";
        }
    }
    else
    {
        ViewData["Title"] = "جميع المنتجات";
    }
}

@* Token antifalsificación para solicitudes AJAX *@
@Html.AntiForgeryToken()

@section Styles {
    <link rel="stylesheet" href="~/css/products-page.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/product-display.css" asp-append-version="true" />
}

<div class="products-header">
    <div class="container">
        <div class="products-title-container">
            <div class="breadcrumb-container">
                <a asp-controller="Home" asp-action="Index" class="breadcrumb-item">الرئيسية</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-item">@ViewData["Title"]</span>
            </div>
            <h1>@ViewData["Title"]</h1>
        </div>
    </div>
</div>

<div class="container">
    <div class="products-container">
        <div class="products-sidebar">
            <div class="price-filter-container">
                <h3 class="price-filter-title">تصفية حسب السعر</h3>
                <input type="range" class="price-range-slider" min="0" max="100" value="100" id="priceRangeSlider">
                <div class="price-inputs">
                    <input type="text" class="price-input" value="0 ر.ع" id="minPrice" readonly>
                    <input type="text" class="price-input" value="100 ر.ع" id="maxPrice" readonly>
                </div>
                <button class="price-filter-button" id="applyPriceFilter">تطبيق التصفية</button>
            </div>

            <div class="top-rated-container">
                <h3 class="top-rated-title">المنتجات الأكثر مبيعاً</h3>
                @{
                    var topRatedProducts = Model.OrderByDescending(p => p.Price).Take(4).ToList();
                }
                @foreach (var product in topRatedProducts)
                {
                    <div class="top-rated-product">
                        @if (!string.IsNullOrEmpty(product.ImageUrl))
                        {
                            <img src="@product.ImageUrl" class="top-rated-img" alt="@product.Name">
                        }
                        else
                        {
                            <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@product.Name" class="top-rated-img" alt="@product.Name">
                        }
                        <div class="top-rated-info">
                            <h4 class="top-rated-name">@product.Name</h4>
                            <div class="top-rated-price">@product.Price.ToString("N0") <span class="currency">ر.ع</span></div>
                        </div>
                    </div>
                }
            </div>
        </div>

        <div class="products-main">
            <div class="view-options">
                <div class="view-mode">
                    <div class="view-mode-option" data-view="grid-16">
                        <i class="bi bi-grid-3x3-gap-fill"></i>
                    </div>
                    <div class="view-mode-option" data-view="grid-6">
                        <i class="bi bi-grid-3x3"></i>
                    </div>
                    <div class="view-mode-option" data-view="grid-4">
                        <i class="bi bi-grid-fill"></i>
                    </div>
                    <div class="view-mode-option active" data-view="grid-2">
                        <i class="bi bi-layout-split"></i>
                    </div>
                    <div class="products-count">
                        تظهر: <span>@Model.Count</span> من أصل <span>@Model.TotalItems</span> منتج
                    </div>
                </div>
                <div class="sort-options">
                    <span class="sort-label">الترتيب:</span>
                    <select class="sort-select" id="productSort">
                        <option value="default">الترتيب الافتراضي</option>
                        <option value="newest">الأحدث</option>
                        <option value="price-asc">السعر: من الأقل إلى الأعلى</option>
                        <option value="price-desc">السعر: من الأعلى إلى الأقل</option>
                    </select>
                </div>
                <div class="items-per-page">
                    <span class="items-per-page-label">عدد المنتجات:</span>
                    <select class="items-per-page-select" id="itemsPerPage">
                        <option value="6">6</option>
                        <option value="12">12</option>
                        <option value="16">16</option>
                        <option value="24">24</option>
                        <option value="36">36</option>
                    </select>
                </div>
                <div class="compare-button-container">
                    <button class="btn btn-outline-primary btn-sm" id="compareButton">
                        <i class="bi bi-arrow-left-right me-1"></i> عرض المقارنة <span class="badge rounded-pill bg-danger compare-button-count">0</span>
                    </button>
                </div>
            </div>

            <div class="product-grid">
                @foreach (var item in Model)
                {
                    <div class="product-item animate__animated animate__fadeInUp">
                        <div class="product-item-img-container">
                            @if (!string.IsNullOrEmpty(item.ImageUrl))
                            {
                                <img src="@item.ImageUrl" class="product-item-img" alt="@item.Name">
                            }
                            else
                            {
                                <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@item.Name" class="product-item-img" alt="@item.Name">
                            }

                            <div class="product-item-discount">-@item.DiscountPercentage%</div>

                            <div class="product-item-availability @(item.IsAvailable ? "available" : "not-available")">
                                @(item.IsAvailable ? "متوفر" : "غير متوفر")
                            </div>

                            <div class="product-item-actions">
                                <button class="product-item-action-btn add-to-cart-btn" data-product-id="@item.Id" title="إضافة للسلة">
                                    <i class="bi bi-cart-plus"></i>
                                </button>
                                <button class="product-item-action-btn add-to-wishlist-btn" data-product-id="@item.Id" title="إضافة للمفضلة">
                                    <i class="bi bi-heart"></i>
                                </button>
                                <button class="product-item-action-btn add-to-compare-btn" data-product-id="@item.Id" title="إضافة للمقارنة">
                                    <i class="bi bi-arrow-left-right"></i>
                                </button>
                            </div>

                            @if (User.IsInRole("Admin"))
                            {
                                <div class="product-actions-menu">
                                    <button class="product-action-btn toggle-carousel-btn @(item.ShowInCarousel ? "active" : "")"
                                            data-product-id="@item.Id"
                                            title="@(item.ShowInCarousel ? "إزالة من الشريط المتحرك" : "إضافة إلى الشريط المتحرك")">
                                        <i class="bi bi-collection-play"></i>
                                    </button>

                                    <button class="product-action-btn toggle-availability-btn @(item.IsAvailable ? "available" : "not-available")"
                                            data-product-id="@item.Id"
                                            title="@(item.IsAvailable ? "تعيين كغير متوفر" : "تعيين كمتوفر")">
                                        <i class="bi bi-@(item.IsAvailable ? "bag-check" : "bag-x")"></i>
                                    </button>
                                </div>
                            }
                        </div>
                        <div class="product-item-info">
                            <h3 class="product-item-title">@item.Name</h3>
                            <div class="product-item-price">
                                <div class="product-item-price-current">@item.DiscountedPrice.ToString("N0") ر.ع</div>
                                <div class="product-item-price-original">@item.Price.ToString("N0") ر.ع</div>
                            </div>
                        </div>
                        <a href="/Products/Details/@item.Id" class="product-link"></a>
                    </div>
                }
            </div>

            <!-- Pagination -->
            <div class="pagination-container mt-4">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        @{
                            var prevDisabled = !Model.HasPreviousPage ? "disabled" : "";
                            var nextDisabled = !Model.HasNextPage ? "disabled" : "";
                        }

                        <li class="page-item @prevDisabled">
                            <a class="page-link" asp-action="Index" asp-route-pageNumber="1" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory" aria-label="First">
                                <i class="bi bi-chevron-double-right"></i>
                            </a>
                        </li>
                        <li class="page-item @prevDisabled">
                            <a class="page-link" asp-action="Index" asp-route-pageNumber="@(Model.PageIndex - 1)" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory" aria-label="Previous">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>

                        @{
                            int startPage = Math.Max(1, Model.PageIndex - 2);
                            int endPage = Math.Min(Model.TotalPages, startPage + 4);

                            if (endPage - startPage < 4 && Model.TotalPages > 4)
                            {
                                startPage = Math.Max(1, endPage - 4);
                            }
                        }

                        @for (int i = startPage; i <= endPage; i++)
                        {
                            <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                                <a class="page-link" asp-action="Index" asp-route-pageNumber="@i" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory">@i</a>
                            </li>
                        }

                        <li class="page-item @nextDisabled">
                            <a class="page-link" asp-action="Index" asp-route-pageNumber="@(Model.PageIndex + 1)" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory" aria-label="Next">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item @nextDisabled">
                            <a class="page-link" asp-action="Index" asp-route-pageNumber="@Model.TotalPages" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory" aria-label="Last">
                                <i class="bi bi-chevron-double-left"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            @if (User.IsInRole("Admin"))
            {
                <div class="text-center mt-4">
                    <a asp-action="Create" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i> إضافة منتج جديد
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Inicializar controles de visualización
            initViewControls();
            initPriceSlider();
            initSortControls();
            initWishlistButtons();
            initCompareButtons();
            initCartButtons();
            initItemsPerPage();
            updateCompareCount();

            // تهيئة أزرار إضافة المنتج إلى الشريط المتحرك وتبديل حالة التوفر (للمدير فقط)
            if (document.querySelector('.toggle-carousel-btn')) {
                initCarouselButtons();
                initAvailabilityButtons();
            }
        });

        function initViewControls() {
            const productGrid = document.querySelector('.product-grid');
            const viewModeOptions = document.querySelectorAll('.view-mode-option');

            // Eventos para controles de vista
            viewModeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const view = this.getAttribute('data-view');
                    console.log('Changing view to:', view);

                    // Quitar clases activas
                    viewModeOptions.forEach(o => o.classList.remove('active'));

                    // Aplicar nuevo estilo
                    this.classList.add('active');

                    // Cambiar el número de columnas según la vista
                    if (view === 'grid-16') {
                        productGrid.classList.remove('grid-6', 'grid-4', 'grid-2');
                        productGrid.classList.add('grid-16');
                        console.log('Applied grid-16 class');
                    } else if (view === 'grid-6') {
                        productGrid.classList.remove('grid-16', 'grid-4', 'grid-2');
                        productGrid.classList.add('grid-6');
                        console.log('Applied grid-6 class');
                    } else if (view === 'grid-4') {
                        productGrid.classList.remove('grid-16', 'grid-6', 'grid-2');
                        productGrid.classList.add('grid-4');
                        console.log('Applied grid-4 class');
                    } else if (view === 'grid-2') {
                        productGrid.classList.remove('grid-16', 'grid-6', 'grid-4');
                        productGrid.classList.add('grid-2');
                        console.log('Applied grid-2 class');
                    }

                    // Mostrar mensaje de confirmación
                    showToast('تم تغيير طريقة العرض');

                    // Guardar preferencia
                    localStorage.setItem('productViewMode', view);
                });
            });

            // Cargar preferencia guardada
            const savedView = localStorage.getItem('productViewMode') || 'grid-2';
            const savedViewOption = document.querySelector(`.view-mode-option[data-view="${savedView}"]`);

            if (savedViewOption) {
                savedViewOption.click();
            }
        }

        function initPriceSlider() {
            const slider = document.getElementById('priceRangeSlider');
            const minPrice = document.getElementById('minPrice');
            const maxPrice = document.getElementById('maxPrice');
            const applyButton = document.getElementById('applyPriceFilter');

            slider.addEventListener('input', function() {
                maxPrice.value = this.value + ' ر.ع';
            });

            applyButton.addEventListener('click', function() {
                filterProductsByPrice();
            });
        }

        function filterProductsByPrice() {
            const maxPriceValue = parseInt(document.getElementById('priceRangeSlider').value);
            const productItems = document.querySelectorAll('.product-item');
            let visibleCount = 0;
            let totalCount = productItems.length;

            productItems.forEach(item => {
                const priceElement = item.querySelector('.product-item-price-current');
                if (priceElement) {
                    // استخراج قيمة السعر من النص
                    const priceText = priceElement.textContent;
                    const priceValue = parseFloat(priceText.replace(/[^\d.]/g, ''));

                    if (priceValue <= maxPriceValue) {
                        item.style.display = '';
                        visibleCount++;
                    } else {
                        item.style.display = 'none';
                    }
                }
            });

            // تحديث عداد المنتجات المعروضة
            updateProductCount(visibleCount, totalCount);

            // عرض رسالة للمستخدم
            showToast(`تم تطبيق التصفية: ${visibleCount} من أصل ${totalCount} منتج`);
        }

        function updateProductCount(visibleCount, totalCount) {
            const countElement = document.querySelector('.products-count span:first-child');
            if (countElement) {
                countElement.textContent = visibleCount;
            }
        }

        function initSortControls() {
            // Esta función ahora solo inicializa los controles
            // El evento de cambio se maneja en el DOMContentLoaded
        }

        // وظيفة فرز المنتجات
        function sortProducts(sortValue) {
            const productGrid = document.querySelector('.product-grid');
            if (!productGrid) {
                console.error('Product grid not found');
                return;
            }

            const products = Array.from(document.querySelectorAll('.product-item'));
            console.log('Found', products.length, 'products to sort');

            if (products.length === 0) {
                console.error('No product items found');
                return;
            }

            products.sort((a, b) => {
                if (sortValue === 'price-asc') {
                    const priceA = getPriceFromItem(a);
                    const priceB = getPriceFromItem(b);
                    console.log('Comparing prices:', priceA, priceB);
                    return priceA - priceB;
                } else if (sortValue === 'price-desc') {
                    const priceA = getPriceFromItem(a);
                    const priceB = getPriceFromItem(b);
                    console.log('Comparing prices (desc):', priceA, priceB);
                    return priceB - priceA;
                } else {
                    // Por defecto, mantener el orden original
                    return 0;
                }
            });

            // Reordenar los productos en el DOM
            products.forEach(product => {
                productGrid.appendChild(product);
            });

            // إظهار رسالة توست للمستخدم
            showToast('تم ترتيب المنتجات');
        }

        function getPriceFromItem(item) {
            const priceElement = item.querySelector('.product-item-price-current');
            if (priceElement) {
                const priceText = priceElement.textContent;
                // استخراج الأرقام فقط من النص
                const priceValue = parseInt(priceText.replace(/[^\d]/g, ''));
                console.log('Extracted price:', priceValue, 'from', priceText);
                return priceValue;
            }
            console.warn('Price element not found in item');
            return 0;
        }

        // تهيئة أزرار المفضلة
        function initWishlistButtons() {
            const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');

            wishlistButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');

                    // إضافة تأثير حركي للزر
                    this.classList.add('adding');

                    // إرسال طلب AJAX لإضافة/إزالة المنتج من المفضلة
                    $.ajax({
                        url: '/Wishlist/AddToWishlist',
                        type: 'POST',
                        data: {
                            productId: productId,
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: (response) => {
                            if (response.success) {
                                // تحديث حالة الزر
                                if (response.isInWishlist) {
                                    this.classList.add('active');
                                    this.querySelector('i').classList.remove('bi-heart');
                                    this.querySelector('i').classList.add('bi-heart-fill');
                                    showToast('تمت إضافة المنتج إلى المفضلة');
                                } else {
                                    this.classList.remove('active');
                                    this.querySelector('i').classList.remove('bi-heart-fill');
                                    this.querySelector('i').classList.add('bi-heart');
                                    showToast('تمت إزالة المنتج من المفضلة');
                                }

                                // تحديث عداد المفضلة مباشرة
                                const wishlistBadge = document.querySelector('.wishlist-count');
                                if (wishlistBadge) {
                                    wishlistBadge.textContent = response.count;

                                    // إضافة تأثير حركي للعداد
                                    wishlistBadge.classList.add('pulse');
                                    wishlistBadge.style.backgroundColor = '#6a0dad';

                                    // إزالة التأثير بعد فترة
                                    setTimeout(() => {
                                        wishlistBadge.classList.remove('pulse');
                                        wishlistBadge.style.backgroundColor = '';
                                    }, 1500);
                                }

                                // لا نطلق حدث تحديث المفضلة لتجنب التحديثات المتكررة
                                // العداد تم تحديثه مباشرة أعلاه
                            } else {
                                showToast('حدث خطأ أثناء تحديث المفضلة');
                            }

                            // إزالة تأثير الإضافة
                            this.classList.remove('adding');
                        },
                        error: () => {
                            showToast('حدث خطأ أثناء تحديث المفضلة');
                            this.classList.remove('adding');
                        }
                    });
                });
            });

            // لا نحدث حالة الأزرار عند التحميل لتجنب طلبات غير ضرورية
            // الأزرار ستحدث حالتها عند الحاجة فقط
        }

        // تحديث حالة أزرار المفضلة
        function updateWishlistButtonsState() {
            $.ajax({
                url: '/Wishlist/GetWishlistItems',
                type: 'GET',
                success: function(data) {
                    if (data && data.items) {
                        $('.add-to-wishlist-btn').each(function() {
                            const productId = parseInt($(this).data('product-id'));
                            const button = $(this);

                            if (data.items.includes(productId)) {
                                button.addClass('active');
                                button.find('i').removeClass('bi-heart').addClass('bi-heart-fill');
                            } else {
                                button.removeClass('active');
                                button.find('i').removeClass('bi-heart-fill').addClass('bi-heart');
                            }
                        });
                    }
                }
            });
        }

        // تحديث عداد المفضلة فوراً
        function updateWishlistCountImmediate() {
            console.log('🎯 تحديث عداد المفضلة فوراً من Products/Index.cshtml');
            $.ajax({
                url: '/Wishlist/GetWishlistItemsCount',
                type: 'GET',
                success: function(response) {
                    const wishlistBadge = document.querySelector('.wishlist-count');
                    if (wishlistBadge) {
                        wishlistBadge.textContent = response.count;

                        // إضافة تأثير حركي
                        wishlistBadge.classList.add('pulse');
                        setTimeout(() => {
                            wishlistBadge.classList.remove('pulse');
                        }, 1000);

                        console.log('✅ تم تحديث عداد المفضلة إلى:', response.count);
                    }
                }
            });
        }

        // تحديث عداد المفضلة للتوافق مع الكود القديم
        function updateWishlistCount() {
            updateWishlistCountImmediate();
        }

        // Inicializar botones de comparación
        function initCompareButtons() {
            const compareButtons = document.querySelectorAll('.add-to-compare-btn');

            // Cargar productos en comparación
            let compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            // Marcar botones de productos que ya están en comparación
            compareButtons.forEach(button => {
                const productId = button.getAttribute('data-product-id');
                const productCard = button.closest('.product-card');

                if (compareList.includes(productId)) {
                    button.classList.add('active');
                    productCard.classList.add('in-compare');
                }
            });

            // Añadir evento de clic a los botones
            compareButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    const productCard = this.closest('.product-card');

                    // Alternar estado activo
                    this.classList.toggle('active');
                    productCard.classList.toggle('in-compare');

                    // Actualizar lista de comparación
                    if (this.classList.contains('active')) {
                        // Limitar a 4 productos en comparación
                        if (compareList.length >= 4) {
                            // Eliminar el primer producto de la lista
                            const removedId = compareList.shift();
                            // Actualizar UI para el producto eliminado
                            const removedButton = document.querySelector(`.add-to-compare-btn[data-product-id="${removedId}"]`);
                            if (removedButton) {
                                removedButton.classList.remove('active');
                                removedButton.closest('.product-card').classList.remove('in-compare');
                            }
                        }

                        // Añadir a comparación si no existe
                        if (!compareList.includes(productId)) {
                            compareList.push(productId);
                            showToast('تمت إضافة المنتج إلى المقارنة');
                        }
                    } else {
                        // Eliminar de comparación
                        compareList = compareList.filter(id => id !== productId);
                        showToast('تمت إزالة المنتج من المقارنة');
                    }

                    // Guardar en localStorage
                    localStorage.setItem('compareList', JSON.stringify(compareList));

                    // تحديث عدد المنتجات في المقارنة
                    updateCompareCount();

                    // إطلاق حدث تحديث المقارنة
                    $(document).trigger('compare:updated');
                });
            });
        }

        // تحديث عدد المنتجات في المقارنة
        function updateCompareCount() {
            const compareList = JSON.parse(localStorage.getItem('compareList')) || [];
            const compareCountBadges = document.querySelectorAll('.compare-count');
            const compareButtonCount = document.querySelector('.compare-button-count');

            // تحديث جميع شارات العد في الصفحة
            compareCountBadges.forEach(badge => {
                badge.textContent = compareList.length;

                if (compareList.length > 0) {
                    badge.classList.remove('d-none');
                } else {
                    badge.classList.add('d-none');
                }
            });

            // تحديث عداد زر عرض المقارنة
            if (compareButtonCount) {
                compareButtonCount.textContent = compareList.length;

                if (compareList.length > 0) {
                    compareButtonCount.classList.remove('d-none');
                } else {
                    compareButtonCount.classList.add('d-none');
                }
            }
        }

        // Inicializar botones de añadir al carrito
        function initCartButtons() {
            const cartButtons = document.querySelectorAll('.add-to-cart-btn');

            cartButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');

                    // Enviar solicitud AJAX para añadir al carrito
                    $.ajax({
                        url: '/Cart/AddToCart',
                        type: 'POST',
                        data: {
                            productId: productId,
                            quantity: 1,
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                showToast('تمت إضافة المنتج إلى السلة');
                                // Actualizar contador del carrito
                                updateCartCount();
                            } else {
                                showToast('حدث خطأ أثناء إضافة المنتج');
                            }
                        },
                        error: function() {
                            showToast('حدث خطأ أثناء إضافة المنتج');
                        }
                    });
                });
            });
        }

        // Actualizar contador del carrito
        function updateCartCount() {
            $.ajax({
                url: '/Cart/GetCartItemsCount',
                type: 'GET',
                success: function(response) {
                    const cartBadge = document.querySelector('.cart-count');
                    if (cartBadge) {
                        cartBadge.textContent = response.count;

                        // Animar el contador
                        cartBadge.classList.add('pulse');
                        setTimeout(() => {
                            cartBadge.classList.remove('pulse');
                        }, 1000);
                    }
                }
            });
        }

        // Crear página de comparación
        function createComparePage() {
            const compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            if (compareList.length === 0) {
                showToast('لا توجد منتجات للمقارنة');
                return;
            }

            // Crear URL con parámetros de consulta
            const url = '/Products/Compare?ids=' + compareList.join(',');
            window.location.href = url;
        }

        // تغيير حجم الصفحة
        function changePageSize(pageSize) {
            // حفظ التفضيل
            localStorage.setItem('itemsPerPage', pageSize);

            // إعادة تحميل الصفحة مع حجم الصفحة الجديد
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('pageSize', pageSize);
            currentUrl.searchParams.set('pageNumber', '1'); // العودة إلى الصفحة الأولى عند تغيير حجم الصفحة
            window.location.href = currentUrl.toString();
        }

        // إضافة مستمعي الأحداث للأزرار
        document.addEventListener('DOMContentLoaded', function() {
            // زر المقارنة
            const compareButton = document.getElementById('compareButton');
            if (compareButton) {
                compareButton.addEventListener('click', createComparePage);
            }

            // قائمة عدد المنتجات
            const itemsPerPageSelect = document.getElementById('itemsPerPage');
            if (itemsPerPageSelect) {
                itemsPerPageSelect.addEventListener('change', function() {
                    changePageSize(this.value);
                });
            }

            // قائمة الترتيب
            const sortSelect = document.getElementById('productSort');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    const sortValue = this.value;
                    sortProducts(sortValue);
                });
            }
        });

        // Inicializar selector de número de productos por página
        function initItemsPerPage() {
            const itemsPerPageSelect = document.getElementById('itemsPerPage');

            // Cargar preferencia guardada (لكن لا نطبقها هنا لأننا نستخدم التقسيم من الخادم)
            const savedItemsPerPage = localStorage.getItem('itemsPerPage') || '@pageSize';

            // تعيين القيمة المحددة في القائمة المنسدلة
            if (itemsPerPageSelect) {
                itemsPerPageSelect.value = savedItemsPerPage;
            }
        }

        // تهيئة أزرار إضافة المنتج إلى الشريط المتحرك (للمدير فقط)
        function initCarouselButtons() {
            const carouselButtons = document.querySelectorAll('.toggle-carousel-btn');

            carouselButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = parseInt(this.getAttribute('data-product-id'));
                    const button = this;

                    // إرسال طلب AJAX لتبديل حالة عرض المنتج في الشريط المتحرك
                    $.ajax({
                        url: '/Products/ToggleCarousel/' + productId,
                        type: 'POST',
                        data: {
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                // تحديث حالة الزر
                                if (response.showInCarousel) {
                                    button.classList.add('active');
                                    button.setAttribute('title', 'إزالة من الشريط المتحرك');
                                } else {
                                    button.classList.remove('active');
                                    button.setAttribute('title', 'إضافة إلى الشريط المتحرك');
                                }

                                // عرض رسالة نجاح
                                showToast(response.message);
                            } else {
                                // عرض رسالة خطأ
                                showToast('حدث خطأ أثناء تحديث المنتج');
                            }
                        },
                        error: function() {
                            showToast('حدث خطأ أثناء تحديث المنتج');
                        }
                    });
                });
            });
        }

        // تهيئة أزرار تبديل حالة توفر المنتج (للمدير فقط)
        function initAvailabilityButtons() {
            const availabilityButtons = document.querySelectorAll('.toggle-availability-btn');

            availabilityButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    const button = this;
                    const productCard = button.closest('.product-card');

                    // إرسال طلب AJAX لتبديل حالة توفر المنتج
                    $.ajax({
                        url: '/Products/ToggleAvailability/' + productId,
                        type: 'POST',
                        data: {
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                // تحديث حالة الزر
                                if (response.isAvailable) {
                                    button.classList.remove('not-available');
                                    button.classList.add('available');
                                    button.setAttribute('title', 'تعيين كغير متوفر');
                                    button.querySelector('i').classList.remove('bi-bag-x');
                                    button.querySelector('i').classList.add('bi-bag-check');

                                    // تحديث شارة التوفر
                                    let availabilityBadge = productCard.querySelector('.availability-badge');
                                    if (availabilityBadge) {
                                        availabilityBadge.classList.remove('not-available');
                                        availabilityBadge.classList.add('available');
                                        availabilityBadge.textContent = 'متوفر';
                                    }
                                } else {
                                    button.classList.remove('available');
                                    button.classList.add('not-available');
                                    button.setAttribute('title', 'تعيين كمتوفر');
                                    button.querySelector('i').classList.remove('bi-bag-check');
                                    button.querySelector('i').classList.add('bi-bag-x');

                                    // تحديث شارة التوفر
                                    let availabilityBadge = productCard.querySelector('.availability-badge');
                                    if (availabilityBadge) {
                                        availabilityBadge.classList.remove('available');
                                        availabilityBadge.classList.add('not-available');
                                        availabilityBadge.textContent = 'غير متوفر';
                                    }
                                }

                                // عرض رسالة نجاح
                                showToast(response.message);
                            } else {
                                // عرض رسالة خطأ
                                showToast('حدث خطأ أثناء تحديث المنتج');
                            }
                        },
                        error: function() {
                            showToast('حدث خطأ أثناء تحديث المنتج');
                        }
                    });
                });
            });
        }

        function showToast(message) {
            // Crear elemento toast si no existe
            let toast = document.getElementById('toast-notification');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-notification';
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.right = '20px';
                toast.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '12px 20px';
                toast.style.borderRadius = '4px';
                toast.style.zIndex = '1000';
                toast.style.transition = 'opacity 0.5s ease-in-out';
                toast.style.opacity = '0';
                toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                document.body.appendChild(toast);
            }

            // Mostrar mensaje
            toast.textContent = message;
            toast.style.opacity = '1';

            // Ocultar después de 3 segundos
            setTimeout(() => {
                toast.style.opacity = '0';
            }, 3000);
        }
    </script>
}
