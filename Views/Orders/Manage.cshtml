@model IEnumerable<Abayat.Models.Order>

@{
    ViewData["Title"] = "إدارة الطلبات";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">
            @if (ViewData["FilteredUserName"] != null)
            {
                <span>طلبات @ViewData["FilteredUserName"]</span>
            }
            else
            {
                @ViewData["Title"]
            }
        </h1>
        <p class="lead mb-4">
            @if (ViewData["FilteredUserName"] != null)
            {
                <span>إدارة طلبات العميل @ViewData["FilteredUserName"] ومتابعة حالتها</span>
                <br />
                <a href="@Url.Action("Manage")" class="btn btn-outline-light btn-sm mt-2">
                    <i class="bi bi-arrow-left"></i> العودة إلى جميع الطلبات
                </a>
            }
            else
            {
                <span>إدارة طلبات العملاء وتحديث حالتها</span>
            }
        </p>
    </div>
</div>

<div class="container py-5">
    @Html.AntiForgeryToken()
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="form-card slide-in-right">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="form-title">قائمة الطلبات</h2>
            <div class="d-flex gap-2">
                <div class="order-filter">
                    <select id="customerFilter" class="form-select">
                        <option value="">جميع العملاء</option>
                        @{
                            var customers = Model.Select(o => new { o.UserId, UserName = o.User?.Name ?? o.FullName }).Distinct().ToList();
                            foreach (var customer in customers)
                            {
                                <option value="@customer.UserId">@customer.UserName</option>
                            }
                        }
                    </select>
                </div>
                <div class="order-filter">
                    <select id="statusFilter" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="0">قيد الانتظار</option>
                        <option value="1">تم التأكيد</option>
                        <option value="2">قيد التجهيز</option>
                        <option value="3">تم الشحن</option>
                        <option value="4">تم التسليم</option>
                        <option value="5">تم الإلغاء</option>
                    </select>
                </div>
            </div>
        </div>

        @if (ViewData["NoOrders"] != null)
        {
            <div class="text-center py-5">
                <i class="bi bi-bag-x display-1 text-muted mb-3"></i>
                <h3>لا توجد طلبات</h3>
                <p class="text-muted mb-4">@ViewData["NoOrders"]</p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i> تأكد من أن الطلبات يتم حفظها في قاعدة البيانات عند إنشائها.
                </div>
            </div>
        }
        else if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover" id="ordersTable">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in Model)
                        {
                            <tr data-status="@((int)order.Status)" data-user-id="@order.UserId">
                                <td>#@order.Id</td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-bold">@(order.User?.Name ?? order.FullName)</span>
                                        <small class="text-muted">@order.FullName</small>
                                    </div>
                                </td>
                                <td>@order.OrderDate.ToString("yyyy/MM/dd HH:mm")</td>
                                <td><span class="currency">ر.ع</span> @order.TotalAmount.ToString("N0")</td>
                                <td class="order-status-cell" data-order-id="@order.Id">
                                    <span class="order-status-text status-@(order.Status.ToString().ToLower())">
                                        <i class="bi @GetStatusIcon(order.Status)"></i>
                                        <span>@GetStatusDisplayName(order.Status)</span>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-primary dropdown-toggle action-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots-vertical"></i> الإجراءات
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li>
                                                    <a href="#" class="dropdown-item status-details" data-order-id="@order.Id">
                                                        <i class="bi bi-info-circle"></i> عرض التفاصيل
                                                    </a>
                                                </li>
                                                @if (order.Status != OrderStatus.Cancelled)
                                                {
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Pending ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Pending)" data-status-name="@GetStatusDisplayName(OrderStatus.Pending)">
                                                            <i class="bi bi-hourglass"></i> قيد الانتظار
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Confirmed ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Confirmed)" data-status-name="@GetStatusDisplayName(OrderStatus.Confirmed)">
                                                            <i class="bi bi-check-circle"></i> تم التأكيد
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Processing ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Processing)" data-status-name="@GetStatusDisplayName(OrderStatus.Processing)">
                                                            <i class="bi bi-gear"></i> قيد التجهيز
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Shipped ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Shipped)" data-status-name="@GetStatusDisplayName(OrderStatus.Shipped)">
                                                            <i class="bi bi-truck"></i> تم الشحن
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Delivered ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Delivered)" data-status-name="@GetStatusDisplayName(OrderStatus.Delivered)">
                                                            <i class="bi bi-check-all"></i> تم التسليم
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Cancelled ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Cancelled)" data-status-name="@GetStatusDisplayName(OrderStatus.Cancelled)">
                                                            <i class="bi bi-x-circle"></i> تم الإلغاء
                                                        </a>
                                                    </li>
                                                }
                                                @if (order.Status == OrderStatus.Cancelled)
                                                {
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <span class="dropdown-item disabled">
                                                            <i class="bi bi-lock"></i> لا يمكن تعديل الطلب الملغي
                                                        </span>
                                                    </li>
                                                }
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-bag-x display-1 text-muted mb-3"></i>
                <h3>لا توجد طلبات</h3>
                <p class="text-muted mb-4">لا توجد طلبات في النظام حالياً.</p>
            </div>
        }
    </div>
</div>

<!-- Modal عرض تفاصيل الطلب -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderDetailsModalLabel">تفاصيل الطلب</h5>
                <button type="button" class="btn-close close-modal" aria-label="Close" id="closeModalBtn" tabindex="0"></button>
            </div>
            <div class="modal-body p-0">
                <div id="orderDetailsContent">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



@section Styles {
    <link rel="stylesheet" href="~/css/order-actions.css" asp-append-version="true" />
    <style>
        .order-filter {
            min-width: 200px;
        }

        .customer-info {
            display: flex;
            flex-direction: column;
        }

        .customer-name {
            font-weight: bold;
        }

        .customer-fullname {
            font-size: 0.85rem;
            color: #6c757d;
        }

        #ordersTable th {
            white-space: nowrap;
        }

        /* تنسيقات حالة الطلب */
        .order-status-cell {
            text-align: center;
        }

        .order-status-text {
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 4px;
            background-color: transparent;
            min-width: 120px;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .order-status-text i {
            margin-left: 8px;
            font-size: 1.1rem;
        }

        /* ألوان نصوص حالات الطلب - بدون حدود */
        .status-pending {
            color: #ff6b35;
        }

        .status-confirmed {
            color: #007bff;
        }

        .status-processing {
            color: #6f42c1;
        }

        .status-shipped {
            color: #0dcaf0;
        }

        .status-delivered {
            color: #198754;
        }

        .status-cancelled {
            color: #dc3545;
        }

        /* تنسيقات القائمة المنسدلة */
        .dropdown-menu {
            min-width: 180px;
            padding: 0.5rem 0;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 8px 15px;
            display: flex;
            align-items: center;
            color: #212529;
            text-decoration: none;
            transition: all 0.2s;
        }

        .dropdown-item i {
            margin-left: 8px;
            width: 20px;
            text-align: center;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #212529;
            text-decoration: none;
        }

        .dropdown-item.active {
            background-color: #f8f9fa;
            color: #212529;
            font-weight: bold;
            position: relative;
        }

        .dropdown-item.active::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #6c757d;
        }

        /* تنسيقات أزرار الإجراءات */
        .action-buttons {
            display: flex;
            justify-content: center;
        }

        .action-btn {
            width: auto;
            text-align: center;
            padding: 6px 12px;
        }

        /* مؤشر التحميل */
        .status-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        @@media (max-width: 768px) {
            .d-flex.gap-2 {
                flex-direction: column;
            }

            .order-filter {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .order-status-badge {
                min-width: auto;
                width: 100%;
            }
        }
    </style>
}

@section Scripts {
    <script>
        console.log('بدء تحميل JavaScript لصفحة إدارة الطلبات');

        $(document).ready(function () {
            console.log('تم تحميل jQuery وصفحة إدارة الطلبات جاهزة');

            // اختبار وجود عناصر القائمة المنسدلة
            console.log('عدد عناصر .dropdown-item:', $('.dropdown-item').length);
            console.log('عدد عناصر .dropdown-menu:', $('.dropdown-menu').length);

            // اختبار النقر على أي عنصر في الصفحة
            $('body').on('click', function(e) {
                console.log('تم النقر على:', e.target);
            });
            // تصفية الطلبات حسب الحالة والعميل
            function applyFilters() {
                var status = $('#statusFilter').val();
                var userId = $('#customerFilter').val();

                // إعادة عرض جميع الصفوف
                $('#ordersTable tbody tr').hide();

                // تطبيق الفلتر
                var selector = '#ordersTable tbody tr';

                if (status !== '') {
                    selector += '[data-status="' + status + '"]';
                }

                if (userId !== '') {
                    selector += '[data-user-id="' + userId + '"]';
                }

                $(selector).show();

                // عرض رسالة إذا لم تكن هناك نتائج
                if ($(selector).length === 0) {
                    if ($('#no-results-message').length === 0) {
                        $('#ordersTable tbody').append('<tr id="no-results-message"><td colspan="6" class="text-center py-3">لا توجد طلبات تطابق معايير البحث</td></tr>');
                    }
                } else {
                    $('#no-results-message').remove();
                }
            }

            // تطبيق الفلتر عند تغيير الحالة
            $('#statusFilter').on('change', function() {
                applyFilters();
            });

            // تطبيق الفلتر عند تغيير العميل
            $('#customerFilter').on('change', function() {
                applyFilters();
            });

            // عرض تفاصيل الطلب في نافذة منبثقة
            $('.view-order-details').on('click', function (e) {
                e.preventDefault();
                var orderId = $(this).data('order-id');
                $('#orderDetailsModalLabel').text('تفاصيل الطلب #' + orderId);
                $('#orderDetailsContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
                $('#orderDetailsModal').modal('show');

                // تحميل تفاصيل الطلب
                $.ajax({
                    url: '/Orders/Details/' + orderId,
                    type: 'GET',
                    success: function (data) {
                        var orderDetails = $(data).find('.row').first();
                        $('#orderDetailsContent').html(orderDetails);

                        // تطبيق التنسيق الجديد على المحتوى
                        $('#orderDetailsContent .col-lg-8').addClass('col-md-8').removeClass('col-lg-8');
                        $('#orderDetailsContent .col-lg-4').addClass('col-md-4').removeClass('col-lg-4');
                        $('#orderDetailsContent .form-card').removeClass('form-card slide-in-right slide-in-left');
                        $('#orderDetailsContent .row').addClass('g-0');
                        $('#orderDetailsContent .col-md-8').addClass('border-end');
                        $('#orderDetailsContent .form-title').addClass('section-title border-bottom pb-2 mb-3').removeClass('form-title');
                    },
                    error: function () {
                        $('#orderDetailsContent').html('<div class="alert alert-danger">حدث خطأ أثناء تحميل تفاصيل الطلب</div>');
                    }
                });
            });

            // معالجة أحداث النافذة المنبثقة
            var modal = $('#orderDetailsModal');

            // قبل فتح النافذة المنبثقة
            modal.on('show.bs.modal', function () {
                // إعادة تعيين المحتوى إذا كان فارغًا
                if ($('#orderDetailsContent').is(':empty')) {
                    $('#orderDetailsContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
                }
            });

            // قبل إغلاق النافذة المنبثقة
            modal.on('hide.bs.modal', function () {
                // إزالة التركيز من جميع العناصر داخل النافذة المنبثقة
                $('#closeModalBtn, #closeModalFooterBtn').blur();
                document.activeElement.blur();
            });

            // بعد إغلاق النافذة المنبثقة
            modal.on('hidden.bs.modal', function () {
                // إعادة تعيين المحتوى
                $('#orderDetailsContent').html('');
                // نقل التركيز إلى عنصر آمن في الصفحة
                setTimeout(function() {
                    // نقل التركيز إلى الصفحة الرئيسية
                    document.body.focus();
                }, 10);
            });

            // معالجة أزرار الإغلاق
            $('.close-modal').on('click', function() {
                // الحصول على معرف النافذة المنبثقة
                var modalElement = $(this).closest('.modal');

                // إغلاق النافذة المنبثقة باستخدام Bootstrap API
                var modalInstance = bootstrap.Modal.getInstance(modalElement[0]);
                if (modalInstance) {
                    modalInstance.hide();
                }

                return false;
            });

            // إضافة معالج عام للنقر على أي زر إغلاق
            $(document).on('click', '[data-bs-dismiss="modal"]', function() {
                var modalElement = $(this).closest('.modal')[0];
                var modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                }
                return false;
            });

            // دالة للحصول على اسم الحالة بناءً على الرقم
            function getStatusName(statusId) {
                switch (parseInt(statusId)) {
                    case 0: return 'pending';
                    case 1: return 'confirmed';
                    case 2: return 'processing';
                    case 3: return 'shipped';
                    case 4: return 'delivered';
                    case 5: return 'cancelled';
                    default: return 'unknown';
                }
            }

            // دالة للحصول على أيقونة الحالة بناءً على الرقم
            function getStatusIcon(statusId) {
                switch (parseInt(statusId)) {
                    case 0: return 'bi-hourglass';
                    case 1: return 'bi-check-circle';
                    case 2: return 'bi-gear';
                    case 3: return 'bi-truck';
                    case 4: return 'bi-check-all';
                    case 5: return 'bi-x-circle';
                    default: return 'bi-question-circle';
                }
            }

            // تحديث حالة الطلب باستخدام AJAX - نسخة مبسطة للاختبار
            $(document).on('click', '.dropdown-item', function(e) {
                e.preventDefault();
                console.log('تم النقر على عنصر القائمة المنسدلة');

                var $this = $(this);
                var orderId = $this.data('order-id');
                var status = $this.data('status');
                var statusName = $this.data('status-name');

                console.log('معرف الطلب:', orderId);
                console.log('الحالة الجديدة:', status);
                console.log('اسم الحالة:', statusName);

                // إذا كان الرابط هو "عرض التفاصيل"
                if ($this.hasClass('status-details')) {
                    console.log('عرض تفاصيل الطلب');
                    return;
                }

                // التحقق من وجود بيانات الحالة
                if (!orderId || status === undefined || !statusName) {
                    console.log('بيانات غير مكتملة - تم تجاهل النقر');
                    console.log('orderId:', orderId, 'status:', status, 'statusName:', statusName);
                    return;
                }

                // التأكد من وجود رمز CSRF
                var token = $('input[name="__RequestVerificationToken"]').val();
                if (!token) {
                    console.log('رمز CSRF غير موجود');
                    alert('خطأ: رمز الأمان غير موجود');
                    return;
                }

                console.log('بدء عملية تحديث الحالة...');
                console.log('رمز CSRF:', token);

                // إرسال طلب AJAX
                $.ajax({
                    url: '/Orders/UpdateStatus',
                    type: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    data: {
                        id: orderId,
                        status: status,
                        __RequestVerificationToken: token
                    },
                    beforeSend: function() {
                        console.log('إرسال الطلب...');
                        $this.prop('disabled', true);
                    },
                    success: function(response) {
                        console.log('نجح الطلب! الاستجابة:', response);

                        if (response && response.success) {
                            // تحديث النص في الجدول
                            var statusCell = $('.order-status-cell[data-order-id="' + orderId + '"]');
                            var statusText = statusCell.find('.order-status-text span');
                            statusText.text(statusName);

                            // تحديث الصف
                            statusCell.closest('tr').attr('data-status', status);

                            // إظهار رسالة نجاح
                            alert('تم تحديث حالة الطلب بنجاح!');
                        } else {
                            console.log('فشل في التحديث:', response);
                            alert('فشل في تحديث حالة الطلب: ' + (response.message || 'خطأ غير معروف'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('خطأ في AJAX:');
                        console.log('Status:', status);
                        console.log('Error:', error);
                        console.log('Response:', xhr.responseText);

                        alert('حدث خطأ أثناء تحديث حالة الطلب');
                    },
                    complete: function() {
                        $this.prop('disabled', false);
                    }
                });
            });
        });
    </script>
}

@functions {
    string GetStatusBadgeClass(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bg-warning",
            OrderStatus.Confirmed => "bg-info",
            OrderStatus.Processing => "bg-primary",
            OrderStatus.Shipped => "bg-info",
            OrderStatus.Delivered => "bg-success",
            OrderStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    string GetStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "قيد الانتظار",
            OrderStatus.Confirmed => "تم التأكيد",
            OrderStatus.Processing => "قيد التجهيز",
            OrderStatus.Shipped => "تم الشحن",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "تم الإلغاء",
            _ => "غير معروف"
        };
    }

    string GetStatusIcon(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bi-hourglass",
            OrderStatus.Confirmed => "bi-check-circle",
            OrderStatus.Processing => "bi-gear",
            OrderStatus.Shipped => "bi-truck",
            OrderStatus.Delivered => "bi-check-all",
            OrderStatus.Cancelled => "bi-x-circle",
            _ => "bi-question-circle"
        };
    }
}
