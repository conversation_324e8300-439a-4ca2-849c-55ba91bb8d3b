@model Abayat.Models.Order

@{
    ViewData["Title"] = "تفاصيل الطلب";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">تفاصيل الطلب #@Model.Id</h1>
        <p class="lead mb-4">عرض تفاصيل الطلب وحالته</p>
    </div>
</div>

<div class="container py-5">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row g-0">
        <div class="col-md-8 border-end">
            <div class="p-4">
                <h3 class="section-title border-bottom pb-2 mb-3">المنتجات</h3>

                <div class="table-responsive">
                    <table class="table table-hover order-products-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.OrderItems != null)
                            {
                                foreach (var item in Model.OrderItems)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (item.Product != null && !string.IsNullOrEmpty(item.Product.ImageUrl))
                                                {
                                                    <img src="@item.Product.ImageUrl" alt="@item.ProductName" class="order-item-img me-3">
                                                }
                                                else
                                                {
                                                    <img src="https://placehold.co/100x100/6a0dad/ffffff?text=@item.ProductName" alt="@item.ProductName" class="order-item-img me-3">
                                                }
                                                <div>
                                                    <h5 class="mb-0">@item.ProductName</h5>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="currency">ر.ع</span> @item.UnitPrice.ToString("N0")</td>
                                        <td>@item.Quantity</td>
                                        <td><span class="currency">ر.ع</span> @item.TotalPrice.ToString("N0")</td>
                                    </tr>
                                }
                            }
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end fw-bold">المجموع الكلي:</td>
                                <td class="fw-bold total-amount"><span class="currency">ر.ع</span> @Model.TotalAmount.ToString("N0")</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="p-4">
                <h3 class="section-title border-bottom pb-2 mb-3">معلومات الطلب</h3>

                <div class="order-info">
                    <div class="info-item">
                        <div class="d-flex justify-content-between">
                            <span class="info-label">رقم الطلب</span>
                            <span class="info-value">#@Model.Id</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="d-flex justify-content-between">
                            <span class="info-label">تاريخ الطلب</span>
                            <span class="info-value">@Model.OrderDate.ToString("yyyy/MM/dd HH:mm")</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="d-flex justify-content-between">
                            <span class="info-label">حالة الطلب</span>
                            <span class="badge @GetStatusBadgeClass(Model.Status)">
                                @GetStatusDisplayName(Model.Status)
                            </span>
                        </div>
                    </div>

                    <hr class="my-3" />

                    <div class="info-item">
                        <span class="info-label d-block">الاسم</span>
                        <span class="info-value d-block">@Model.FullName</span>
                    </div>

                    <div class="info-item">
                        <span class="info-label d-block">رقم الهاتف</span>
                        <span class="info-value d-block">@Model.PhoneNumber</span>
                    </div>

                    <div class="info-item">
                        <span class="info-label d-block">العنوان</span>
                        <span class="info-value d-block">@Model.Address</span>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="info-item">
                            <span class="info-label d-block">ملاحظات</span>
                            <span class="info-value d-block">@Model.Notes</span>
                        </div>
                    }

                    @if (Model.Status == OrderStatus.Pending || Model.Status == OrderStatus.Confirmed)
                    {
                        <div class="mt-4">
                            <form asp-action="Cancel" method="post">
                                <input type="hidden" name="id" value="@Model.Id" />
                                <button type="submit" class="btn btn-danger w-100" onclick="return confirm('هل أنت متأكد من إلغاء الطلب؟ لا يمكن التراجع عن هذا الإجراء')">
                                    <i class="bi bi-x-circle"></i> إلغاء الطلب
                                </button>
                            </form>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i> العودة للطلبات
        </a>
    </div>
</div>

@section Styles {
    <style>
        .order-item-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
}

@functions {
    string GetStatusBadgeClass(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bg-warning",
            OrderStatus.Confirmed => "bg-info",
            OrderStatus.Processing => "bg-primary",
            OrderStatus.Shipped => "bg-info",
            OrderStatus.Delivered => "bg-success",
            OrderStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    string GetStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "قيد الانتظار",
            OrderStatus.Confirmed => "تم التأكيد",
            OrderStatus.Processing => "قيد التجهيز",
            OrderStatus.Shipped => "تم الشحن",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "تم الإلغاء",
            _ => "غير معروف"
        };
    }
}
