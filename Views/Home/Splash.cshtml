@{
    Layout = null;
    ViewData["Title"] = "راعي المخور";
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <style>
        body {
            background-color: #6a0dad;
            overflow: hidden;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
        }

        .splash-container {
            text-align: center;
            color: white;
        }

        .splash-logo {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 1rem;
            opacity: 0;
        }

        .splash-tagline {
            font-size: 1.5rem;
            opacity: 0;
        }

        .splash-icon {
            font-size: 5rem;
            margin-bottom: 2rem;
            opacity: 0;
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div id="splash-icon" class="splash-icon">⋆</div>
        <h1 id="splash-logo" class="splash-logo">راعي المخور</h1>
        <p id="splash-tagline" class="splash-tagline">لتصميم فاخر وأنيق يناسب مناسباتك الخاصة</p>
    </div>

    <script>
        // تحريك العناصر باستخدام JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك الأيقونة
            var icon = document.getElementById('splash-icon');
            setTimeout(function() {
                icon.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                icon.style.opacity = '1';
                icon.style.transform = 'scale(1)';
            }, 100);

            // تحريك الشعار
            var logo = document.getElementById('splash-logo');
            setTimeout(function() {
                logo.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                logo.style.opacity = '1';
                logo.style.transform = 'translateY(0)';
            }, 300);

            // تحريك العبارة الوصفية
            var tagline = document.getElementById('splash-tagline');
            setTimeout(function() {
                tagline.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                tagline.style.opacity = '1';
                tagline.style.transform = 'translateY(0)';
            }, 600);

            // الانتقال إلى الصفحة الرئيسية بعد ثانيتين
            setTimeout(function() {
                window.location.href = '/Home/Index';
            }, 2500);
        });

        // تعيين الحالة الأولية للعناصر
        document.getElementById('splash-icon').style.transform = 'scale(0.5)';
        document.getElementById('splash-logo').style.transform = 'translateY(40px)';
        document.getElementById('splash-tagline').style.transform = 'translateY(40px)';
    </script>
</body>
</html>
