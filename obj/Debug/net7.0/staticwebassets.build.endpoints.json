{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Identity/css/site.css", "AssetFile": "Identity/css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUDqPyFDscAdhBrKAZt6ovv+dDZgiMWb96mKVq7q47Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DUDqPyFDscAdhBrKAZt6ovv+dDZgiMWb96mKVq7q47Q="}]}, {"Route": "Identity/favicon.ico", "AssetFile": "Identity/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "32038"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0="}]}, {"Route": "Identity/js/site.js", "AssetFile": "Identity/js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0="}]}, {"Route": "Identity/lib/bootstrap/LICENSE", "AssetFile": "Identity/lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70538"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51319"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117439"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117516"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5850"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105138"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4646"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35330"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5827"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4718"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41570"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71584"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192271"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53479"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71451"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192214"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111710"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "204136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "661035"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "208492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "425643"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "327261"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "139019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288320"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "72016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222508"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "148168"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "289522"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "59511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "217145"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "Identity/lib/jquery-validation/LICENSE.md", "AssetFile": "Identity/lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "Identity/lib/jquery-validation/dist/additional-methods.js", "AssetFile": "Identity/lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43184"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="}]}, {"Route": "Identity/lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "Identity/lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18467"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w="}]}, {"Route": "Identity/lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "Identity/lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48676"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yazfaIh2SXu8rPenyD2f36pKgrkv5XT+DQCDpZ/eDao=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yazfaIh2SXu8rPenyD2f36pKgrkv5XT+DQCDpZ/eDao="}]}, {"Route": "Identity/lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "Identity/lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23261"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F6h55Qw6sweK+t7SiOJX+2bpSAa3b/fnlrVCJvmEj1A=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F6h55Qw6sweK+t7SiOJX+2bpSAa3b/fnlrVCJvmEj1A="}]}, {"Route": "Identity/lib/jquery/LICENSE.txt", "AssetFile": "Identity/lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "Identity/lib/jquery/dist/jquery.js", "AssetFile": "Identity/lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]}, {"Route": "Identity/lib/jquery/dist/jquery.min.js", "AssetFile": "Identity/lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]}, {"Route": "Identity/lib/jquery/dist/jquery.min.map", "AssetFile": "Identity/lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Oct 2022 05:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]}, {"Route": "css/admin-dashboard.8uatryui52.css", "AssetFile": "css/admin-dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5816"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IP8jIYZUDK44JjkCXHRAloarW9wtDbQJR8LEUpmidVk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:42:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8uatryui52"}, {"Name": "integrity", "Value": "sha256-IP8jIYZUDK44JjkCXHRAloarW9wtDbQJR8LEUpmidVk="}, {"Name": "label", "Value": "css/admin-dashboard.css"}]}, {"Route": "css/admin-dashboard.css", "AssetFile": "css/admin-dashboard.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5816"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IP8jIYZUDK44JjkCXHRAloarW9wtDbQJR8LEUpmidVk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:42:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IP8jIYZUDK44JjkCXHRAloarW9wtDbQJR8LEUpmidVk="}]}, {"Route": "css/animations.css", "AssetFile": "css/animations.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3377"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"an3mPfJv/bxSKqLaeLUZ3fCNmqyoExZML/8N/H8U3aw=\""}, {"Name": "Last-Modified", "Value": "Sat, 17 May 2025 12:28:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-an3mPfJv/bxSKqLaeLUZ3fCNmqyoExZML/8N/H8U3aw="}]}, {"Route": "css/animations.urkx21zm8o.css", "AssetFile": "css/animations.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3377"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"an3mPfJv/bxSKqLaeLUZ3fCNmqyoExZML/8N/H8U3aw=\""}, {"Name": "Last-Modified", "Value": "Sat, 17 May 2025 12:28:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "urkx21zm8o"}, {"Name": "integrity", "Value": "sha256-an3mPfJv/bxSKqLaeLUZ3fCNmqyoExZML/8N/H8U3aw="}, {"Name": "label", "Value": "css/animations.css"}]}, {"Route": "css/badge-position-fix.css", "AssetFile": "css/badge-position-fix.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1688"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NZuSQcu4Kpt8q0NQkqkf008A59zV93KT5IXu4lL72IQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 16:59:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NZuSQcu4Kpt8q0NQkqkf008A59zV93KT5IXu4lL72IQ="}]}, {"Route": "css/badge-position-fix.htl75v7fof.css", "AssetFile": "css/badge-position-fix.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1688"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NZuSQcu4Kpt8q0NQkqkf008A59zV93KT5IXu4lL72IQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 16:59:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "htl75v7fof"}, {"Name": "integrity", "Value": "sha256-NZuSQcu4Kpt8q0NQkqkf008A59zV93KT5IXu4lL72IQ="}, {"Name": "label", "Value": "css/badge-position-fix.css"}]}, {"Route": "css/carousel-custom.4axo0o8gb8.css", "AssetFile": "css/carousel-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1987"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hGE3TWl542v8W08W4k79YO2jtAI/TF6iRJP5hJ2Pc30=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 14:20:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4axo0o8gb8"}, {"Name": "integrity", "Value": "sha256-hGE3TWl542v8W08W4k79YO2jtAI/TF6iRJP5hJ2Pc30="}, {"Name": "label", "Value": "css/carousel-custom.css"}]}, {"Route": "css/carousel-custom.css", "AssetFile": "css/carousel-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1987"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hGE3TWl542v8W08W4k79YO2jtAI/TF6iRJP5hJ2Pc30=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 14:20:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hGE3TWl542v8W08W4k79YO2jtAI/TF6iRJP5hJ2Pc30="}]}, {"Route": "css/cart.bjxwgamrx0.css", "AssetFile": "css/cart.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xwmZQ2chIF4F0v7jwU5ZkN5VRPJ95y8nZ0Cc/yg94Ho=\""}, {"Name": "Last-Modified", "Value": "Thu, 15 May 2025 20:44:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bjxwgamrx0"}, {"Name": "integrity", "Value": "sha256-xwmZQ2chIF4F0v7jwU5ZkN5VRPJ95y8nZ0Cc/yg94Ho="}, {"Name": "label", "Value": "css/cart.css"}]}, {"Route": "css/cart.css", "AssetFile": "css/cart.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xwmZQ2chIF4F0v7jwU5ZkN5VRPJ95y8nZ0Cc/yg94Ho=\""}, {"Name": "Last-Modified", "Value": "Thu, 15 May 2025 20:44:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xwmZQ2chIF4F0v7jwU5ZkN5VRPJ95y8nZ0Cc/yg94Ho="}]}, {"Route": "css/display-options.css", "AssetFile": "css/display-options.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8336"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RQlYWM3DR9OTj5RoEzxMStTvIQ/IxE8SkJJtZcJeX4U=\""}, {"Name": "Last-Modified", "Value": "Thu, 08 May 2025 19:16:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RQlYWM3DR9OTj5RoEzxMStTvIQ/IxE8SkJJtZcJeX4U="}]}, {"Route": "css/display-options.vn2lmwmp1g.css", "AssetFile": "css/display-options.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8336"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RQlYWM3DR9OTj5RoEzxMStTvIQ/IxE8SkJJtZcJeX4U=\""}, {"Name": "Last-Modified", "Value": "Thu, 08 May 2025 19:16:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vn2lmwmp1g"}, {"Name": "integrity", "Value": "sha256-RQlYWM3DR9OTj5RoEzxMStTvIQ/IxE8SkJJtZcJeX4U="}, {"Name": "label", "Value": "css/display-options.css"}]}, {"Route": "css/header-ameera.88e0k1mrs5.css", "AssetFile": "css/header-ameera.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3629"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CKDXaqU87kdWZ8sjVPx0w+T6RwggGud9nFTQ4n9wvmM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 18:09:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "88e0k1mrs5"}, {"Name": "integrity", "Value": "sha256-CKDXaqU87kdWZ8sjVPx0w+T6RwggGud9nFTQ4n9wvmM="}, {"Name": "label", "Value": "css/header-ameera.css"}]}, {"Route": "css/header-ameera.css", "AssetFile": "css/header-ameera.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3629"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CKDXaqU87kdWZ8sjVPx0w+T6RwggGud9nFTQ4n9wvmM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 18:09:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKDXaqU87kdWZ8sjVPx0w+T6RwggGud9nFTQ4n9wvmM="}]}, {"Route": "css/header-icons.css", "AssetFile": "css/header-icons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1298"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XJooe1e+oSE50LN2bS8anCw9Qz4hExKUxa44zyyd2fA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 17:08:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XJooe1e+oSE50LN2bS8anCw9Qz4hExKUxa44zyyd2fA="}]}, {"Route": "css/header-icons.g7pj4cazn1.css", "AssetFile": "css/header-icons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1298"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XJooe1e+oSE50LN2bS8anCw9Qz4hExKUxa44zyyd2fA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 17:08:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g7pj4cazn1"}, {"Name": "integrity", "Value": "sha256-XJooe1e+oSE50LN2bS8anCw9Qz4hExKUxa44zyyd2fA="}, {"Name": "label", "Value": "css/header-icons.css"}]}, {"Route": "css/hide-secondary-nav.cq41qncp2j.css", "AssetFile": "css/hide-secondary-nav.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1480"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NOQW3UVvdDog75K/GQHk0sRAxw7qC0h6pXrjR45FOY8=\""}, {"Name": "Last-Modified", "Value": "Sun, 18 May 2025 19:50:59 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cq41qncp2j"}, {"Name": "integrity", "Value": "sha256-NOQW3UVvdDog75K/GQHk0sRAxw7qC0h6pXrjR45FOY8="}, {"Name": "label", "Value": "css/hide-secondary-nav.css"}]}, {"Route": "css/hide-secondary-nav.css", "AssetFile": "css/hide-secondary-nav.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1480"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NOQW3UVvdDog75K/GQHk0sRAxw7qC0h6pXrjR45FOY8=\""}, {"Name": "Last-Modified", "Value": "Sun, 18 May 2025 19:50:59 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NOQW3UVvdDog75K/GQHk0sRAxw7qC0h6pXrjR45FOY8="}]}, {"Route": "css/login-register-buttons.css", "AssetFile": "css/login-register-buttons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2934"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wfbatODrUwcPfx/QzWPxwbo3u3SGiWxDAr6BuHp2QAA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 06:48:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wfbatODrUwcPfx/QzWPxwbo3u3SGiWxDAr6BuHp2QAA="}]}, {"Route": "css/login-register-buttons.x8klhfpnk9.css", "AssetFile": "css/login-register-buttons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2934"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wfbatODrUwcPfx/QzWPxwbo3u3SGiWxDAr6BuHp2QAA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 06:48:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x8klhfpnk9"}, {"Name": "integrity", "Value": "sha256-wfbatODrUwcPfx/QzWPxwbo3u3SGiWxDAr6BuHp2QAA="}, {"Name": "label", "Value": "css/login-register-buttons.css"}]}, {"Route": "css/mobile-header.css", "AssetFile": "css/mobile-header.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5122"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kxqdJFBrZYaCaXr+hWw45LEKpXnmomnNOn9FUFO5szw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 19:20:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kxqdJFBrZYaCaXr+hWw45LEKpXnmomnNOn9FUFO5szw="}]}, {"Route": "css/mobile-header.d8wl7mhfgc.css", "AssetFile": "css/mobile-header.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5122"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kxqdJFBrZYaCaXr+hWw45LEKpXnmomnNOn9FUFO5szw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 19:20:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d8wl7mhfgc"}, {"Name": "integrity", "Value": "sha256-kxqdJFBrZYaCaXr+hWw45LEKpXnmomnNOn9FUFO5szw="}, {"Name": "label", "Value": "css/mobile-header.css"}]}, {"Route": "css/mobile-menu.css", "AssetFile": "css/mobile-menu.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UDCSqNw9nDaSP0NgRE9UEJ+rezycoCrYsZFKuFc5JrY=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:24:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UDCSqNw9nDaSP0NgRE9UEJ+rezycoCrYsZFKuFc5JrY="}]}, {"Route": "css/mobile-menu.wg1xii7wok.css", "AssetFile": "css/mobile-menu.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UDCSqNw9nDaSP0NgRE9UEJ+rezycoCrYsZFKuFc5JrY=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:24:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wg1xii7wok"}, {"Name": "integrity", "Value": "sha256-UDCSqNw9nDaSP0NgRE9UEJ+rezycoCrYsZFKuFc5JrY="}, {"Name": "label", "Value": "css/mobile-menu.css"}]}, {"Route": "css/mobile-nav.css", "AssetFile": "css/mobile-nav.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2211"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YpajnkANtQL4ueT7NAW2EqdMYnW3fqgbRgcLklojX5M=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 18:27:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YpajnkANtQL4ueT7NAW2EqdMYnW3fqgbRgcLklojX5M="}]}, {"Route": "css/mobile-nav.y3tt5r0bfr.css", "AssetFile": "css/mobile-nav.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2211"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YpajnkANtQL4ueT7NAW2EqdMYnW3fqgbRgcLklojX5M=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 18:27:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y3tt5r0bfr"}, {"Name": "integrity", "Value": "sha256-YpajnkANtQL4ueT7NAW2EqdMYnW3fqgbRgcLklojX5M="}, {"Name": "label", "Value": "css/mobile-nav.css"}]}, {"Route": "css/navbar-custom.5zo4emlyzu.css", "AssetFile": "css/navbar-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3010"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j/KPq8kBnGyy+cTBmD++fgL3FB23KIFBTm7h8vEVwG8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 06:24:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5zo4emlyzu"}, {"Name": "integrity", "Value": "sha256-j/KPq8kBnGyy+cTBmD++fgL3FB23KIFBTm7h8vEVwG8="}, {"Name": "label", "Value": "css/navbar-custom.css"}]}, {"Route": "css/navbar-custom.css", "AssetFile": "css/navbar-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3010"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j/KPq8kBnGyy+cTBmD++fgL3FB23KIFBTm7h8vEVwG8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 06:24:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j/KPq8kBnGyy+cTBmD++fgL3FB23KIFBTm7h8vEVwG8="}]}, {"Route": "css/navbar-font.6v0jesy39u.css", "AssetFile": "css/navbar-font.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3tblWIWQaWf7aLyTtUWjjihdln3c+Fark7mlrCrj3JQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:40:36 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6v0jesy39u"}, {"Name": "integrity", "Value": "sha256-3tblWIWQaWf7aLyTtUWjjihdln3c+Fark7mlrCrj3JQ="}, {"Name": "label", "Value": "css/navbar-font.css"}]}, {"Route": "css/navbar-font.css", "AssetFile": "css/navbar-font.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3tblWIWQaWf7aLyTtUWjjihdln3c+Fark7mlrCrj3JQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:40:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3tblWIWQaWf7aLyTtUWjjihdln3c+Fark7mlrCrj3JQ="}]}, {"Route": "css/order-actions.css", "AssetFile": "css/order-actions.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2697"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VSN8AKyHp9+t5fwJZLueFRg8nCglOPHz3acX3nM/Ytg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:53:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VSN8AKyHp9+t5fwJZLueFRg8nCglOPHz3acX3nM/Ytg="}]}, {"Route": "css/order-actions.robqrlnlsf.css", "AssetFile": "css/order-actions.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2697"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VSN8AKyHp9+t5fwJZLueFRg8nCglOPHz3acX3nM/Ytg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:53:40 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "robqrlnlsf"}, {"Name": "integrity", "Value": "sha256-VSN8AKyHp9+t5fwJZLueFRg8nCglOPHz3acX3nM/Ytg="}, {"Name": "label", "Value": "css/order-actions.css"}]}, {"Route": "css/order-tracking.9eddqbsaju.css", "AssetFile": "css/order-tracking.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2216"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"79RXGn0d+rK111p9AZL3fW6IaAOSAwdPq2LFgCvZ2R4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:51:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-79RXGn0d+rK111p9AZL3fW6IaAOSAwdPq2LFgCvZ2R4="}, {"Name": "label", "Value": "css/order-tracking.css"}]}, {"Route": "css/order-tracking.css", "AssetFile": "css/order-tracking.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2216"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"79RXGn0d+rK111p9AZL3fW6IaAOSAwdPq2LFgCvZ2R4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:51:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-79RXGn0d+rK111p9AZL3fW6IaAOSAwdPq2LFgCvZ2R4="}]}, {"Route": "css/product-display.css", "AssetFile": "css/product-display.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4909"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cyl2cxMs3fqp9doLWantA30/A1PgezKE+zDzB8rF9RA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:27:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cyl2cxMs3fqp9doLWantA30/A1PgezKE+zDzB8rF9RA="}]}, {"Route": "css/product-display.p1dayp1wiq.css", "AssetFile": "css/product-display.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4909"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cyl2cxMs3fqp9doLWantA30/A1PgezKE+zDzB8rF9RA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:27:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "p1dayp1wiq"}, {"Name": "integrity", "Value": "sha256-cyl2cxMs3fqp9doLWantA30/A1PgezKE+zDzB8rF9RA="}, {"Name": "label", "Value": "css/product-display.css"}]}, {"Route": "css/products-page.css", "AssetFile": "css/products-page.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12335"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"e1LSd5vFxGIeAiPWOCHy4+tsomqkQ/L8dLPWn3/6XuU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:09:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e1LSd5vFxGIeAiPWOCHy4+tsomqkQ/L8dLPWn3/6XuU="}]}, {"Route": "css/products-page.vnq2fk2oz9.css", "AssetFile": "css/products-page.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12335"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"e1LSd5vFxGIeAiPWOCHy4+tsomqkQ/L8dLPWn3/6XuU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:09:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vnq2fk2oz9"}, {"Name": "integrity", "Value": "sha256-e1LSd5vFxGIeAiPWOCHy4+tsomqkQ/L8dLPWn3/6XuU="}, {"Name": "label", "Value": "css/products-page.css"}]}, {"Route": "css/remove-second-navbar.a47mkjn00m.css", "AssetFile": "css/remove-second-navbar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1562"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dgLjOD6Oym7UH2DELtt8988x7kL5I3h+Cp2jwVc6VVI=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 05:22:30 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a47mkjn00m"}, {"Name": "integrity", "Value": "sha256-dgLjOD6Oym7UH2DELtt8988x7kL5I3h+Cp2jwVc6VVI="}, {"Name": "label", "Value": "css/remove-second-navbar.css"}]}, {"Route": "css/remove-second-navbar.css", "AssetFile": "css/remove-second-navbar.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1562"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dgLjOD6Oym7UH2DELtt8988x7kL5I3h+Cp2jwVc6VVI=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 05:22:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dgLjOD6Oym7UH2DELtt8988x7kL5I3h+Cp2jwVc6VVI="}]}, {"Route": "css/reverse-navbar-items.css", "AssetFile": "css/reverse-navbar-items.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2338"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"46rBl88bCFRVi9fwv/bppDnuNH/s9BLYHPGLYVZEQo8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 05:45:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-46rBl88bCFRVi9fwv/bppDnuNH/s9BLYHPGLYVZEQo8="}]}, {"Route": "css/reverse-navbar-items.rel69c2ik9.css", "AssetFile": "css/reverse-navbar-items.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2338"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"46rBl88bCFRVi9fwv/bppDnuNH/s9BLYHPGLYVZEQo8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 05:45:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rel69c2ik9"}, {"Name": "integrity", "Value": "sha256-46rBl88bCFRVi9fwv/bppDnuNH/s9BLYHPGLYVZEQo8="}, {"Name": "label", "Value": "css/reverse-navbar-items.css"}]}, {"Route": "css/rtl-fixes.3su50rba47.css", "AssetFile": "css/rtl-fixes.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3559"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Vg4vQPJ2BaTR00DX+hDC1afgS7MbtYq0JVENNaLFCQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 16:53:25 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3su50rba47"}, {"Name": "integrity", "Value": "sha256-7Vg4vQPJ2BaTR00DX+hDC1afgS7MbtYq0JVENNaLFCQ="}, {"Name": "label", "Value": "css/rtl-fixes.css"}]}, {"Route": "css/rtl-fixes.css", "AssetFile": "css/rtl-fixes.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3559"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Vg4vQPJ2BaTR00DX+hDC1afgS7MbtYq0JVENNaLFCQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 16:53:25 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7Vg4vQPJ2BaTR00DX+hDC1afgS7MbtYq0JVENNaLFCQ="}]}, {"Route": "css/side-filter.css", "AssetFile": "css/side-filter.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3748"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Lm1h3MZt5cHSfCYIXhjXfOUZQCFSBla8lxfeUlz/SdQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:11:25 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Lm1h3MZt5cHSfCYIXhjXfOUZQCFSBla8lxfeUlz/SdQ="}]}, {"Route": "css/side-filter.ulwnuo15fi.css", "AssetFile": "css/side-filter.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3748"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Lm1h3MZt5cHSfCYIXhjXfOUZQCFSBla8lxfeUlz/SdQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:11:25 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ulwnuo15fi"}, {"Name": "integrity", "Value": "sha256-Lm1h3MZt5cHSfCYIXhjXfOUZQCFSBla8lxfeUlz/SdQ="}, {"Name": "label", "Value": "css/side-filter.css"}]}, {"Route": "css/side-login.8kysxfx9q0.css", "AssetFile": "css/side-login.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3970"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CLUS1ZR+PI6oF4az5C04Z9oEmZLha4togB9B6wlnR0g=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 16:45:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8kysxfx9q0"}, {"Name": "integrity", "Value": "sha256-CLUS1ZR+PI6oF4az5C04Z9oEmZLha4togB9B6wlnR0g="}, {"Name": "label", "Value": "css/side-login.css"}]}, {"Route": "css/side-login.css", "AssetFile": "css/side-login.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3970"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CLUS1ZR+PI6oF4az5C04Z9oEmZLha4togB9B6wlnR0g=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 16:45:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CLUS1ZR+PI6oF4az5C04Z9oEmZLha4togB9B6wlnR0g="}]}, {"Route": "css/site.3eo8uectje.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "54965"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ewOJiPE97j9y3eGT1EhRibmI+3NumNrRyNdS3Onj05w=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 19:12:46 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3eo8uectje"}, {"Name": "integrity", "Value": "sha256-ewOJiPE97j9y3eGT1EhRibmI+3NumNrRyNdS3Onj05w="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "54965"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ewOJiPE97j9y3eGT1EhRibmI+3NumNrRyNdS3Onj05w=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 19:12:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ewOJiPE97j9y3eGT1EhRibmI+3NumNrRyNdS3Onj05w="}]}, {"Route": "css/sticky-nav.css", "AssetFile": "css/sticky-nav.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4572"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AR7o9zPcaO4tmUH7Qzzda+65W9pSi0iDjEFnTam5uyY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:11:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AR7o9zPcaO4tmUH7Qzzda+65W9pSi0iDjEFnTam5uyY="}]}, {"Route": "css/sticky-nav.d54w1ogls8.css", "AssetFile": "css/sticky-nav.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4572"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AR7o9zPcaO4tmUH7Qzzda+65W9pSi0iDjEFnTam5uyY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:11:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d54w1ogls8"}, {"Name": "integrity", "Value": "sha256-AR7o9zPcaO4tmUH7Qzzda+65W9pSi0iDjEFnTam5uyY="}, {"Name": "label", "Value": "css/sticky-nav.css"}]}, {"Route": "css/wishlist-compare.5jwntsrdzt.css", "AssetFile": "css/wishlist-compare.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5369"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j97GgdnFycH4dAH/P+0hsRBgJRoQ9czpbRK98p1AdqI=\""}, {"Name": "Last-Modified", "Value": "Sat, 17 May 2025 17:03:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5jwntsrdzt"}, {"Name": "integrity", "Value": "sha256-j97GgdnFycH4dAH/P+0hsRBgJRoQ9czpbRK98p1AdqI="}, {"Name": "label", "Value": "css/wishlist-compare.css"}]}, {"Route": "css/wishlist-compare.css", "AssetFile": "css/wishlist-compare.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5369"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j97GgdnFycH4dAH/P+0hsRBgJRoQ9czpbRK98p1AdqI=\""}, {"Name": "Last-Modified", "Value": "Sat, 17 May 2025 17:03:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j97GgdnFycH4dAH/P+0hsRBgJRoQ9czpbRK98p1AdqI="}]}, {"Route": "images/404-illustration.i5zppwvnep.svg", "AssetFile": "images/404-illustration.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1723"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"NmcErXMyaZp/gc6UAISlrwSrdgzBwSz2zJR061v7hjI=\""}, {"Name": "Last-Modified", "Value": "Fri, 09 May 2025 11:52:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i5zppwvnep"}, {"Name": "integrity", "Value": "sha256-NmcErXMyaZp/gc6UAISlrwSrdgzBwSz2zJR061v7hjI="}, {"Name": "label", "Value": "images/404-illustration.svg"}]}, {"Route": "images/404-illustration.svg", "AssetFile": "images/404-illustration.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1723"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"NmcErXMyaZp/gc6UAISlrwSrdgzBwSz2zJR061v7hjI=\""}, {"Name": "Last-Modified", "Value": "Fri, 09 May 2025 11:52:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NmcErXMyaZp/gc6UAISlrwSrdgzBwSz2zJR061v7hjI="}]}, {"Route": "images/a1.jpeg", "AssetFile": "images/a1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "379332"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OS9QfO/W2WhI3rzWGaYMiPaalJo3kgGuGo4b2EYNLec=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:35:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OS9QfO/W2WhI3rzWGaYMiPaalJo3kgGuGo4b2EYNLec="}]}, {"Route": "images/a1.tbx6dtwmmi.jpeg", "AssetFile": "images/a1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "379332"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OS9QfO/W2WhI3rzWGaYMiPaalJo3kgGuGo4b2EYNLec=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:35:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tbx6dtwmmi"}, {"Name": "integrity", "Value": "sha256-OS9QfO/W2WhI3rzWGaYMiPaalJo3kgGuGo4b2EYNLec="}, {"Name": "label", "Value": "images/a1.jpeg"}]}, {"Route": "images/a2.2fatrhwnx5.jpeg", "AssetFile": "images/a2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6501"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/ngMMLu5aX5mUqCbaqGAZVtAlt6AWHXHnPpHzczajJI=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:35:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2fatrhwnx5"}, {"Name": "integrity", "Value": "sha256-/ngMMLu5aX5mUqCbaqGAZVtAlt6AWHXHnPpHzczajJI="}, {"Name": "label", "Value": "images/a2.jpeg"}]}, {"Route": "images/a2.jpeg", "AssetFile": "images/a2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "6501"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/ngMMLu5aX5mUqCbaqGAZVtAlt6AWHXHnPpHzczajJI=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:35:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/ngMMLu5aX5mUqCbaqGAZVtAlt6AWHXHnPpHzczajJI="}]}, {"Route": "images/a3.bsqb6wrswg.jpeg", "AssetFile": "images/a3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6488"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"W1JcXPgva9Yor9uZ5m34vGVRooSSvWjvNXNA0X2PLB0=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:36:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bsqb6wrswg"}, {"Name": "integrity", "Value": "sha256-W1JcXPgva9Yor9uZ5m34vGVRooSSvWjvNXNA0X2PLB0="}, {"Name": "label", "Value": "images/a3.jpeg"}]}, {"Route": "images/a3.jpeg", "AssetFile": "images/a3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "6488"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"W1JcXPgva9Yor9uZ5m34vGVRooSSvWjvNXNA0X2PLB0=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:36:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W1JcXPgva9Yor9uZ5m34vGVRooSSvWjvNXNA0X2PLB0="}]}, {"Route": "images/a4.jpeg", "AssetFile": "images/a4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "7681"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:36:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM="}]}, {"Route": "images/a4.r4gocgcpny.jpeg", "AssetFile": "images/a4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7681"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:36:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4gocgcpny"}, {"Name": "integrity", "Value": "sha256-VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM="}, {"Name": "label", "Value": "images/a4.jpeg"}]}, {"Route": "images/b1.a7z62bf56t.jpeg", "AssetFile": "images/b1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6964"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Sopf4V4URPLlYBjZigLyx30EXo6gAcuqe6OC73Da0Vg=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:41:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a7z62bf56t"}, {"Name": "integrity", "Value": "sha256-Sopf4V4URPLlYBjZigLyx30EXo6gAcuqe6OC73Da0Vg="}, {"Name": "label", "Value": "images/b1.jpeg"}]}, {"Route": "images/b1.jpeg", "AssetFile": "images/b1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "6964"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Sopf4V4URPLlYBjZigLyx30EXo6gAcuqe6OC73Da0Vg=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:41:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Sopf4V4URPLlYBjZigLyx30EXo6gAcuqe6OC73Da0Vg="}]}, {"Route": "images/b2.3zp5v47pzk.jpg", "AssetFile": "images/b2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "158841"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"v9HJ+NVOwCw6+tpBKaVXXRJ86YgAWEXtz0s1KCZ2jY8=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:41:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3zp5v47pzk"}, {"Name": "integrity", "Value": "sha256-v9HJ+NVOwCw6+tpBKaVXXRJ86YgAWEXtz0s1KCZ2jY8="}, {"Name": "label", "Value": "images/b2.jpg"}]}, {"Route": "images/b2.jpg", "AssetFile": "images/b2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "158841"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"v9HJ+NVOwCw6+tpBKaVXXRJ86YgAWEXtz0s1KCZ2jY8=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:41:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v9HJ+NVOwCw6+tpBKaVXXRJ86YgAWEXtz0s1KCZ2jY8="}]}, {"Route": "images/b3.jpeg", "AssetFile": "images/b3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "10608"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:41:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw="}]}, {"Route": "images/b3.qsto1bq0yv.jpeg", "AssetFile": "images/b3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10608"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:41:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qsto1bq0yv"}, {"Name": "integrity", "Value": "sha256-2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw="}, {"Name": "label", "Value": "images/b3.jpeg"}]}, {"Route": "images/b4.2g0hd85nwd.jpeg", "AssetFile": "images/b4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14968"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"foYah1MgaKP2ON+de85ANMHs/9Bd4zR1bnbBkuXL1+I=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:41:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2g0hd85nwd"}, {"Name": "integrity", "Value": "sha256-foYah1MgaKP2ON+de85ANMHs/9Bd4zR1bnbBkuXL1+I="}, {"Name": "label", "Value": "images/b4.jpeg"}]}, {"Route": "images/b4.jpeg", "AssetFile": "images/b4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "14968"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"foYah1MgaKP2ON+de85ANMHs/9Bd4zR1bnbBkuXL1+I=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:41:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-foYah1MgaKP2ON+de85ANMHs/9Bd4zR1bnbBkuXL1+I="}]}, {"Route": "images/c1.g0xjhzgmw4.jpg", "AssetFile": "images/c1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19387"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"IDVPC44xIiLfs//PgMdsI9wN8Lz+qRJnm1urFNlel9E=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:39:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0xjhzgmw4"}, {"Name": "integrity", "Value": "sha256-IDVPC44xIiLfs//PgMdsI9wN8Lz+qRJnm1urFNlel9E="}, {"Name": "label", "Value": "images/c1.jpg"}]}, {"Route": "images/c1.jpg", "AssetFile": "images/c1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "19387"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"IDVPC44xIiLfs//PgMdsI9wN8Lz+qRJnm1urFNlel9E=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:39:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDVPC44xIiLfs//PgMdsI9wN8Lz+qRJnm1urFNlel9E="}]}, {"Route": "images/c2.3dnytqrkhm.jpeg", "AssetFile": "images/c2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7768"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:40:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3dnytqrkhm"}, {"Name": "integrity", "Value": "sha256-xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw="}, {"Name": "label", "Value": "images/c2.jpeg"}]}, {"Route": "images/c2.jpeg", "AssetFile": "images/c2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "7768"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:40:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw="}]}, {"Route": "images/c3.aqmotzmrya.webp", "AssetFile": "images/c3.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51884"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"ElLT8JzH8vpts5n+lUSCopOSZPsNJZIGmvtzrBlECWg=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:40:13 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aqmotzmrya"}, {"Name": "integrity", "Value": "sha256-ElLT8JzH8vpts5n+lUSCopOSZPsNJZIGmvtzrBlECWg="}, {"Name": "label", "Value": "images/c3.webp"}]}, {"Route": "images/c3.jpeg", "AssetFile": "images/c3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "5474"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OQ2Hc8A/REDbeHlR7p6D2QrcH3VpiEvXp4o+6yrmQw4=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:40:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OQ2Hc8A/REDbeHlR7p6D2QrcH3VpiEvXp4o+6yrmQw4="}]}, {"Route": "images/c3.ngrnkhdbet.jpeg", "AssetFile": "images/c3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5474"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OQ2Hc8A/REDbeHlR7p6D2QrcH3VpiEvXp4o+6yrmQw4=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:40:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ngrnkhdbet"}, {"Name": "integrity", "Value": "sha256-OQ2Hc8A/REDbeHlR7p6D2QrcH3VpiEvXp4o+6yrmQw4="}, {"Name": "label", "Value": "images/c3.jpeg"}]}, {"Route": "images/c3.webp", "AssetFile": "images/c3.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "51884"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"ElLT8JzH8vpts5n+lUSCopOSZPsNJZIGmvtzrBlECWg=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:40:13 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ElLT8JzH8vpts5n+lUSCopOSZPsNJZIGmvtzrBlECWg="}]}, {"Route": "images/c5.inuyjl9xgq.jpeg", "AssetFile": "images/c5.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9573"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UvlKbAtMn/qZLz2MJ7XIkMmHWKIeUwoHyDSEQMpXj+w=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:40:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "inuyjl9xgq"}, {"Name": "integrity", "Value": "sha256-UvlKbAtMn/qZLz2MJ7XIkMmHWKIeUwoHyDSEQMpXj+w="}, {"Name": "label", "Value": "images/c5.jpeg"}]}, {"Route": "images/c5.jpeg", "AssetFile": "images/c5.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "9573"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UvlKbAtMn/qZLz2MJ7XIkMmHWKIeUwoHyDSEQMpXj+w=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:40:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UvlKbAtMn/qZLz2MJ7XIkMmHWKIeUwoHyDSEQMpXj+w="}]}, {"Route": "images/carousel/0d7440a8-7cb9-4602-9df0-9a88263652ca_بخور٢.jpg", "AssetFile": "images/carousel/0d7440a8-7cb9-4602-9df0-9a88263652ca_بخور٢.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "21285"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:32:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI="}]}, {"Route": "images/carousel/0d7440a8-7cb9-4602-9df0-9a88263652ca_بخور٢.n0w87jnoql.jpg", "AssetFile": "images/carousel/0d7440a8-7cb9-4602-9df0-9a88263652ca_بخور٢.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21285"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:32:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n0w87jnoql"}, {"Name": "integrity", "Value": "sha256-6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI="}, {"Name": "label", "Value": "images/carousel/0d7440a8-7cb9-4602-9df0-9a88263652ca_بخور٢.jpg"}]}, {"Route": "images/carousel/ab507d8b-4575-439b-b9a3-4662256d6169_Screenshot 2025-05-17 at 1.34.25 AM.glz7k71yic.png", "AssetFile": "images/carousel/ab507d8b-4575-439b-b9a3-4662256d6169_Screenshot 2025-05-17 at 1.34.25 AM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2166080"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"9E/m/SfrbM9YdBUIR5ryPRXNZu0s4lcKG+4JG+BId6k=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:35:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "glz7k71yic"}, {"Name": "integrity", "Value": "sha256-9E/m/SfrbM9YdBUIR5ryPRXNZu0s4lcKG+4JG+BId6k="}, {"Name": "label", "Value": "images/carousel/ab507d8b-4575-439b-b9a3-4662256d6169_Screenshot 2025-05-17 at 1.34.25 AM.png"}]}, {"Route": "images/carousel/ab507d8b-4575-439b-b9a3-4662256d6169_Screenshot 2025-05-17 at 1.34.25 AM.png", "AssetFile": "images/carousel/ab507d8b-4575-439b-b9a3-4662256d6169_Screenshot 2025-05-17 at 1.34.25 AM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "2166080"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"9E/m/SfrbM9YdBUIR5ryPRXNZu0s4lcKG+4JG+BId6k=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:35:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9E/m/SfrbM9YdBUIR5ryPRXNZu0s4lcKG+4JG+BId6k="}]}, {"Route": "images/carousel/ba59274b-8c4a-4c20-a1ba-dd13b2509a2b_Screenshot 2025-05-17 at 12.41.30 AM.ay5ug7e3sh.png", "AssetFile": "images/carousel/ba59274b-8c4a-4c20-a1ba-dd13b2509a2b_Screenshot 2025-05-17 at 12.41.30 AM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "929795"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:30:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ay5ug7e3sh"}, {"Name": "integrity", "Value": "sha256-XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0="}, {"Name": "label", "Value": "images/carousel/ba59274b-8c4a-4c20-a1ba-dd13b2509a2b_Screenshot 2025-05-17 at 12.41.30 AM.png"}]}, {"Route": "images/carousel/ba59274b-8c4a-4c20-a1ba-dd13b2509a2b_Screenshot 2025-05-17 at 12.41.30 AM.png", "AssetFile": "images/carousel/ba59274b-8c4a-4c20-a1ba-dd13b2509a2b_Screenshot 2025-05-17 at 12.41.30 AM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "929795"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:30:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0="}]}, {"Route": "images/categories/4ef5438e-5c40-4ce4-a993-a619e3e8e823_0fc3c635-3118-4619-b3a7-44c4fc0115b5.iyou7fbwtc.jpg", "AssetFile": "images/categories/4ef5438e-5c40-4ce4-a993-a619e3e8e823_0fc3c635-3118-4619-b3a7-44c4fc0115b5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31188"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JnOsmPx1G4/sRMQH7hpjjcEqtYJgL2jXH6BghWONwZ4=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:10:04 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyou7fbwtc"}, {"Name": "integrity", "Value": "sha256-JnOsmPx1G4/sRMQH7hpjjcEqtYJgL2jXH6BghWONwZ4="}, {"Name": "label", "Value": "images/categories/4ef5438e-5c40-4ce4-a993-a619e3e8e823_0fc3c635-3118-4619-b3a7-44c4fc0115b5.jpg"}]}, {"Route": "images/categories/4ef5438e-5c40-4ce4-a993-a619e3e8e823_0fc3c635-3118-4619-b3a7-44c4fc0115b5.jpg", "AssetFile": "images/categories/4ef5438e-5c40-4ce4-a993-a619e3e8e823_0fc3c635-3118-4619-b3a7-44c4fc0115b5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "31188"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JnOsmPx1G4/sRMQH7hpjjcEqtYJgL2jXH6BghWONwZ4=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:10:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JnOsmPx1G4/sRMQH7hpjjcEqtYJgL2jXH6BghWONwZ4="}]}, {"Route": "images/categories/52343ccc-d7da-4d5d-b30b-c72235c19272_b3.jpeg", "AssetFile": "images/categories/52343ccc-d7da-4d5d-b30b-c72235c19272_b3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "10608"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:44:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw="}]}, {"Route": "images/categories/52343ccc-d7da-4d5d-b30b-c72235c19272_b3.qsto1bq0yv.jpeg", "AssetFile": "images/categories/52343ccc-d7da-4d5d-b30b-c72235c19272_b3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10608"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:44:32 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qsto1bq0yv"}, {"Name": "integrity", "Value": "sha256-2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw="}, {"Name": "label", "Value": "images/categories/52343ccc-d7da-4d5d-b30b-c72235c19272_b3.jpeg"}]}, {"Route": "images/categories/631bcb87-3c37-49e2-8ade-aeab2f2eca2a_بخور٢.jpg", "AssetFile": "images/categories/631bcb87-3c37-49e2-8ade-aeab2f2eca2a_بخور٢.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "21285"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:12:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI="}]}, {"Route": "images/categories/631bcb87-3c37-49e2-8ade-aeab2f2eca2a_بخور٢.n0w87jnoql.jpg", "AssetFile": "images/categories/631bcb87-3c37-49e2-8ade-aeab2f2eca2a_بخور٢.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21285"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:12:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n0w87jnoql"}, {"Name": "integrity", "Value": "sha256-6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI="}, {"Name": "label", "Value": "images/categories/631bcb87-3c37-49e2-8ade-aeab2f2eca2a_بخور٢.jpg"}]}, {"Route": "images/categories/6f0b6ee4-75aa-4168-9867-4a87c4c3f658_a4.jpeg", "AssetFile": "images/categories/6f0b6ee4-75aa-4168-9867-4a87c4c3f658_a4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "7681"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:42:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM="}]}, {"Route": "images/categories/6f0b6ee4-75aa-4168-9867-4a87c4c3f658_a4.r4gocgcpny.jpeg", "AssetFile": "images/categories/6f0b6ee4-75aa-4168-9867-4a87c4c3f658_a4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7681"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:42:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4gocgcpny"}, {"Name": "integrity", "Value": "sha256-VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM="}, {"Name": "label", "Value": "images/categories/6f0b6ee4-75aa-4168-9867-4a87c4c3f658_a4.jpeg"}]}, {"Route": "images/categories/7d0c1833-918b-4f7a-b844-63cb9a2634e7_ab4.3hub1du3uj.webp", "AssetFile": "images/categories/7d0c1833-918b-4f7a-b844-63cb9a2634e7_ab4.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "61418"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"BwSlGAxwXFMRI9gFEFuvQuaea4/SHpUrbVkA+CQ1jj4=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:06:25 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3hub1du3uj"}, {"Name": "integrity", "Value": "sha256-BwSlGAxwXFMRI9gFEFuvQuaea4/SHpUrbVkA+CQ1jj4="}, {"Name": "label", "Value": "images/categories/7d0c1833-918b-4f7a-b844-63cb9a2634e7_ab4.webp"}]}, {"Route": "images/categories/7d0c1833-918b-4f7a-b844-63cb9a2634e7_ab4.webp", "AssetFile": "images/categories/7d0c1833-918b-4f7a-b844-63cb9a2634e7_ab4.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "61418"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"BwSlGAxwXFMRI9gFEFuvQuaea4/SHpUrbVkA+CQ1jj4=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:06:25 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BwSlGAxwXFMRI9gFEFuvQuaea4/SHpUrbVkA+CQ1jj4="}]}, {"Route": "images/categories/abayas-icon.2nms02u0vg.png", "AssetFile": "images/categories/abayas-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:04:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2nms02u0vg"}, {"Name": "integrity", "Value": "sha256-nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ="}, {"Name": "label", "Value": "images/categories/abayas-icon.png"}]}, {"Route": "images/categories/abayas-icon.png", "AssetFile": "images/categories/abayas-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:04:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ="}]}, {"Route": "images/categories/accessories-icon.2nms02u0vg.png", "AssetFile": "images/categories/accessories-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:07:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2nms02u0vg"}, {"Name": "integrity", "Value": "sha256-nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ="}, {"Name": "label", "Value": "images/categories/accessories-icon.png"}]}, {"Route": "images/categories/accessories-icon.png", "AssetFile": "images/categories/accessories-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:07:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ="}]}, {"Route": "images/categories/b980ccb8-4102-4b08-ba8d-3dbfe857d655_ac2.izpqy7jh2z.webp", "AssetFile": "images/categories/b980ccb8-4102-4b08-ba8d-3dbfe857d655_ac2.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "33794"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:08:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "izpqy7jh2z"}, {"Name": "integrity", "Value": "sha256-DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0="}, {"Name": "label", "Value": "images/categories/b980ccb8-4102-4b08-ba8d-3dbfe857d655_ac2.webp"}]}, {"Route": "images/categories/b980ccb8-4102-4b08-ba8d-3dbfe857d655_ac2.webp", "AssetFile": "images/categories/b980ccb8-4102-4b08-ba8d-3dbfe857d655_ac2.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "33794"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:08:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0="}]}, {"Route": "images/categories/cc1f788a-3bfa-4df9-8c04-9fb9879671cd_hqdefault.1dh8d89gid.jpg", "AssetFile": "images/categories/cc1f788a-3bfa-4df9-8c04-9fb9879671cd_hqdefault.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23324"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5eYpq6c5hXJTLrX8YL529gIFS1KGHdhjJoz/5uUXrds=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Apr 2025 11:27:59 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1dh8d89gid"}, {"Name": "integrity", "Value": "sha256-5eYpq6c5hXJTLrX8YL529gIFS1KGHdhjJoz/5uUXrds="}, {"Name": "label", "Value": "images/categories/cc1f788a-3bfa-4df9-8c04-9fb9879671cd_hqdefault.jpg"}]}, {"Route": "images/categories/cc1f788a-3bfa-4df9-8c04-9fb9879671cd_hqdefault.jpg", "AssetFile": "images/categories/cc1f788a-3bfa-4df9-8c04-9fb9879671cd_hqdefault.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "23324"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5eYpq6c5hXJTLrX8YL529gIFS1KGHdhjJoz/5uUXrds=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Apr 2025 11:27:59 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5eYpq6c5hXJTLrX8YL529gIFS1KGHdhjJoz/5uUXrds="}]}, {"Route": "images/categories/df2817c6-cb8f-4e86-8951-39f14b8bf383_m1.jpeg", "AssetFile": "images/categories/df2817c6-cb8f-4e86-8951-39f14b8bf383_m1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "10542"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:43:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw="}]}, {"Route": "images/categories/df2817c6-cb8f-4e86-8951-39f14b8bf383_m1.jqkg7lmamd.jpeg", "AssetFile": "images/categories/df2817c6-cb8f-4e86-8951-39f14b8bf383_m1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10542"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:43:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jqkg7lmamd"}, {"Name": "integrity", "Value": "sha256-+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw="}, {"Name": "label", "Value": "images/categories/df2817c6-cb8f-4e86-8951-39f14b8bf383_m1.jpeg"}]}, {"Route": "images/categories/f87c55a3-d4bd-4cac-b0ff-05e79da04a06_c2.3dnytqrkhm.jpeg", "AssetFile": "images/categories/f87c55a3-d4bd-4cac-b0ff-05e79da04a06_c2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7768"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:44:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3dnytqrkhm"}, {"Name": "integrity", "Value": "sha256-xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw="}, {"Name": "label", "Value": "images/categories/f87c55a3-d4bd-4cac-b0ff-05e79da04a06_c2.jpeg"}]}, {"Route": "images/categories/f87c55a3-d4bd-4cac-b0ff-05e79da04a06_c2.jpeg", "AssetFile": "images/categories/f87c55a3-d4bd-4cac-b0ff-05e79da04a06_c2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "7768"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:44:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw="}]}, {"Route": "images/categories/fabrics-icon.2nms02u0vg.png", "AssetFile": "images/categories/fabrics-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:05:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2nms02u0vg"}, {"Name": "integrity", "Value": "sha256-nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ="}, {"Name": "label", "Value": "images/categories/fabrics-icon.png"}]}, {"Route": "images/categories/fabrics-icon.png", "AssetFile": "images/categories/fabrics-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:05:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ="}]}, {"Route": "images/categories/mukhwar-icon.2nms02u0vg.png", "AssetFile": "images/categories/mukhwar-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:08:35 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2nms02u0vg"}, {"Name": "integrity", "Value": "sha256-nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ="}, {"Name": "label", "Value": "images/categories/mukhwar-icon.png"}]}, {"Route": "images/categories/mukhwar-icon.png", "AssetFile": "images/categories/mukhwar-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:08:35 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ="}]}, {"Route": "images/k1.jpeg", "AssetFile": "images/k1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "5279"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"q4VqF0WknT0mq7TeOqguxmrAMnxYioiUnp3/xuAPLII=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:36:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q4VqF0WknT0mq7TeOqguxmrAMnxYioiUnp3/xuAPLII="}]}, {"Route": "images/k1.vvfhzmc5z5.jpeg", "AssetFile": "images/k1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5279"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"q4VqF0WknT0mq7TeOqguxmrAMnxYioiUnp3/xuAPLII=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:36:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vvfhzmc5z5"}, {"Name": "integrity", "Value": "sha256-q4VqF0WknT0mq7TeOqguxmrAMnxYioiUnp3/xuAPLII="}, {"Name": "label", "Value": "images/k1.jpeg"}]}, {"Route": "images/k2.6o92510fz9.jpeg", "AssetFile": "images/k2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6540"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"dibprLVJx8JtWrC6q/fFBXDz8pMgD9zUda0Ah7XxFqs=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:37:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6o92510fz9"}, {"Name": "integrity", "Value": "sha256-dibprLVJx8JtWrC6q/fFBXDz8pMgD9zUda0Ah7XxFqs="}, {"Name": "label", "Value": "images/k2.jpeg"}]}, {"Route": "images/k2.jpeg", "AssetFile": "images/k2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "6540"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"dibprLVJx8JtWrC6q/fFBXDz8pMgD9zUda0Ah7XxFqs=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:37:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dibprLVJx8JtWrC6q/fFBXDz8pMgD9zUda0Ah7XxFqs="}]}, {"Route": "images/k3.jpeg", "AssetFile": "images/k3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "10826"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"F5xvVOHYHsYmNmS71e2DG3gc7rNrYWIp3qU8M2MnmAM=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:37:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F5xvVOHYHsYmNmS71e2DG3gc7rNrYWIp3qU8M2MnmAM="}]}, {"Route": "images/k3.v2n1h08d5h.jpeg", "AssetFile": "images/k3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10826"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"F5xvVOHYHsYmNmS71e2DG3gc7rNrYWIp3qU8M2MnmAM=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:37:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v2n1h08d5h"}, {"Name": "integrity", "Value": "sha256-F5xvVOHYHsYmNmS71e2DG3gc7rNrYWIp3qU8M2MnmAM="}, {"Name": "label", "Value": "images/k3.jpeg"}]}, {"Route": "images/k4.jpeg", "AssetFile": "images/k4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "13803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"StkE87xCr0mWdCv39oxwEyaC9bq2G2eEXXB7Wagjvao=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:37:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-StkE87xCr0mWdCv39oxwEyaC9bq2G2eEXXB7Wagjvao="}]}, {"Route": "images/k4.qneeprmq73.jpeg", "AssetFile": "images/k4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"StkE87xCr0mWdCv39oxwEyaC9bq2G2eEXXB7Wagjvao=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:37:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qneeprmq73"}, {"Name": "integrity", "Value": "sha256-StkE87xCr0mWdCv39oxwEyaC9bq2G2eEXXB7Wagjvao="}, {"Name": "label", "Value": "images/k4.jpeg"}]}, {"Route": "images/k5.jpeg", "AssetFile": "images/k5.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "15006"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"1sODnvJtnoCABAyUPmuy75SMo7ddJGymhn8HP7lU1Yc=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:37:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1sODnvJtnoCABAyUPmuy75SMo7ddJGymhn8HP7lU1Yc="}]}, {"Route": "images/k5.qgp5kqv0kz.jpeg", "AssetFile": "images/k5.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15006"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"1sODnvJtnoCABAyUPmuy75SMo7ddJGymhn8HP7lU1Yc=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:37:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qgp5kqv0kz"}, {"Name": "integrity", "Value": "sha256-1sODnvJtnoCABAyUPmuy75SMo7ddJGymhn8HP7lU1Yc="}, {"Name": "label", "Value": "images/k5.jpeg"}]}, {"Route": "images/logo.7qm2e1160w.png", "AssetFile": "images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25869"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"j+GHIHsIUr4FsifVQixkpNPZjRsIjczo0ofW584Hyko=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 18:35:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7qm2e1160w"}, {"Name": "integrity", "Value": "sha256-j+GHIHsIUr4FsifVQixkpNPZjRsIjczo0ofW584Hyko="}, {"Name": "label", "Value": "images/logo.png"}]}, {"Route": "images/logo.png", "AssetFile": "images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "25869"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"j+GHIHsIUr4FsifVQixkpNPZjRsIjczo0ofW584Hyko=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 18:35:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j+GHIHsIUr4FsifVQixkpNPZjRsIjczo0ofW584Hyko="}]}, {"Route": "images/logo1.0dxnr7dp4p.png", "AssetFile": "images/logo1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "27485"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"pI8fmqG6RcxP2hXx49t3H4lSAn/lfd9g0UkE1glkd88=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 18:35:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0dxnr7dp4p"}, {"Name": "integrity", "Value": "sha256-pI8fmqG6RcxP2hXx49t3H4lSAn/lfd9g0UkE1glkd88="}, {"Name": "label", "Value": "images/logo1.png"}]}, {"Route": "images/logo1.png", "AssetFile": "images/logo1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "27485"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"pI8fmqG6RcxP2hXx49t3H4lSAn/lfd9g0UkE1glkd88=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 18:35:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pI8fmqG6RcxP2hXx49t3H4lSAn/lfd9g0UkE1glkd88="}]}, {"Route": "images/m1.jpeg", "AssetFile": "images/m1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "10542"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:38:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw="}]}, {"Route": "images/m1.jqkg7lmamd.jpeg", "AssetFile": "images/m1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10542"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:38:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jqkg7lmamd"}, {"Name": "integrity", "Value": "sha256-+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw="}, {"Name": "label", "Value": "images/m1.jpeg"}]}, {"Route": "images/m2.6wj3vy2934.jpeg", "AssetFile": "images/m2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "358977"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"itH33UhQ+Qb8BMkA+M/mOUcGwdbqqPhgVHw7Tzy8qFc=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:38:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6wj3vy2934"}, {"Name": "integrity", "Value": "sha256-itH33UhQ+Qb8BMkA+M/mOUcGwdbqqPhgVHw7Tzy8qFc="}, {"Name": "label", "Value": "images/m2.jpeg"}]}, {"Route": "images/m2.jpeg", "AssetFile": "images/m2.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "358977"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"itH33UhQ+Qb8BMkA+M/mOUcGwdbqqPhgVHw7Tzy8qFc=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:38:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-itH33UhQ+Qb8BMkA+M/mOUcGwdbqqPhgVHw7Tzy8qFc="}]}, {"Route": "images/m3.erd6xs7vi8.jpeg", "AssetFile": "images/m3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12680"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"1pN+5a9TkVPwO6r0VLMogCMsX7TnXwEpHWXLcqzKRus=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:38:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erd6xs7vi8"}, {"Name": "integrity", "Value": "sha256-1pN+5a9TkVPwO6r0VLMogCMsX7TnXwEpHWXLcqzKRus="}, {"Name": "label", "Value": "images/m3.jpeg"}]}, {"Route": "images/m3.jpeg", "AssetFile": "images/m3.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "12680"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"1pN+5a9TkVPwO6r0VLMogCMsX7TnXwEpHWXLcqzKRus=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:38:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1pN+5a9TkVPwO6r0VLMogCMsX7TnXwEpHWXLcqzKRus="}]}, {"Route": "images/m4.6slw65rivq.jpeg", "AssetFile": "images/m4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9673"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"up+b8OHbc9SAXZeThfVV14pmesLs2IBsIiKa/rVW3x8=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:39:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6slw65rivq"}, {"Name": "integrity", "Value": "sha256-up+b8OHbc9SAXZeThfVV14pmesLs2IBsIiKa/rVW3x8="}, {"Name": "label", "Value": "images/m4.jpeg"}]}, {"Route": "images/m4.jpeg", "AssetFile": "images/m4.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "9673"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"up+b8OHbc9SAXZeThfVV14pmesLs2IBsIiKa/rVW3x8=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:39:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-up+b8OHbc9SAXZeThfVV14pmesLs2IBsIiKa/rVW3x8="}]}, {"Route": "images/m5.57kkn2zbrn.jpeg", "AssetFile": "images/m5.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"QVKkI5YFMookhXyAcbU/QdatyN7YBngrm8ZEMDhdOjc=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:39:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "57kkn2zbrn"}, {"Name": "integrity", "Value": "sha256-QVKkI5YFMookhXyAcbU/QdatyN7YBngrm8ZEMDhdOjc="}, {"Name": "label", "Value": "images/m5.jpeg"}]}, {"Route": "images/m5.jpeg", "AssetFile": "images/m5.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "9803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"QVKkI5YFMookhXyAcbU/QdatyN7YBngrm8ZEMDhdOjc=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Apr 2025 18:39:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QVKkI5YFMookhXyAcbU/QdatyN7YBngrm8ZEMDhdOjc="}]}, {"Route": "images/placeholder-icon.e7b0ez3zfr.png", "AssetFile": "images/placeholder-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Rp1JZf48zqecV94bcffxcW09Mcqbfo+K7yb9wdvI/j0=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:02:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e7b0ez3zfr"}, {"Name": "integrity", "Value": "sha256-Rp1JZf48zqecV94bcffxcW09Mcqbfo+K7yb9wdvI/j0="}, {"Name": "label", "Value": "images/placeholder-icon.png"}]}, {"Route": "images/placeholder-icon.png", "AssetFile": "images/placeholder-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Rp1JZf48zqecV94bcffxcW09Mcqbfo+K7yb9wdvI/j0=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 11:02:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rp1JZf48zqecV94bcffxcW09Mcqbfo+K7yb9wdvI/j0="}]}, {"Route": "images/products/0c9a3122-08ed-4c4b-8965-71f14a66a761_ab2.9h36ung6xi.webp", "AssetFile": "images/products/0c9a3122-08ed-4c4b-8965-71f14a66a761_ab2.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "48422"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"445mFxos0f7GmLKop/+i6xvu1EaHXmfkCnLK3U2KpbM=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:13:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9h36ung6xi"}, {"Name": "integrity", "Value": "sha256-445mFxos0f7GmLKop/+i6xvu1EaHXmfkCnLK3U2KpbM="}, {"Name": "label", "Value": "images/products/0c9a3122-08ed-4c4b-8965-71f14a66a761_ab2.webp"}]}, {"Route": "images/products/0c9a3122-08ed-4c4b-8965-71f14a66a761_ab2.webp", "AssetFile": "images/products/0c9a3122-08ed-4c4b-8965-71f14a66a761_ab2.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "48422"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"445mFxos0f7GmLKop/+i6xvu1EaHXmfkCnLK3U2KpbM=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:13:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-445mFxos0f7GmLKop/+i6xvu1EaHXmfkCnLK3U2KpbM="}]}, {"Route": "images/products/19efcafc-f812-4039-a7b1-0854e98fa50b_Screenshot 2025-05-17 at 12.41.30 AM.ay5ug7e3sh.png", "AssetFile": "images/products/19efcafc-f812-4039-a7b1-0854e98fa50b_Screenshot 2025-05-17 at 12.41.30 AM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "929795"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 20:43:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ay5ug7e3sh"}, {"Name": "integrity", "Value": "sha256-XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0="}, {"Name": "label", "Value": "images/products/19efcafc-f812-4039-a7b1-0854e98fa50b_Screenshot 2025-05-17 at 12.41.30 AM.png"}]}, {"Route": "images/products/19efcafc-f812-4039-a7b1-0854e98fa50b_Screenshot 2025-05-17 at 12.41.30 AM.png", "AssetFile": "images/products/19efcafc-f812-4039-a7b1-0854e98fa50b_Screenshot 2025-05-17 at 12.41.30 AM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "929795"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 20:43:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0="}]}, {"Route": "images/products/262d4bcf-5ae7-4983-8e45-865a3f2c28b6_8ab8e7e9-97f6-4b02-86e5-10b8528758bb.jpg", "AssetFile": "images/products/262d4bcf-5ae7-4983-8e45-865a3f2c28b6_8ab8e7e9-97f6-4b02-86e5-10b8528758bb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "34234"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UWrfufCGzWFWHdce4kCTA/3p+Df3QB2n8kLdKjWeltg=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:12:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UWrfufCGzWFWHdce4kCTA/3p+Df3QB2n8kLdKjWeltg="}]}, {"Route": "images/products/262d4bcf-5ae7-4983-8e45-865a3f2c28b6_8ab8e7e9-97f6-4b02-86e5-10b8528758bb.p23v55vkc2.jpg", "AssetFile": "images/products/262d4bcf-5ae7-4983-8e45-865a3f2c28b6_8ab8e7e9-97f6-4b02-86e5-10b8528758bb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34234"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UWrfufCGzWFWHdce4kCTA/3p+Df3QB2n8kLdKjWeltg=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:12:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "p23v55vkc2"}, {"Name": "integrity", "Value": "sha256-UWrfufCGzWFWHdce4kCTA/3p+Df3QB2n8kLdKjWeltg="}, {"Name": "label", "Value": "images/products/262d4bcf-5ae7-4983-8e45-865a3f2c28b6_8ab8e7e9-97f6-4b02-86e5-10b8528758bb.jpg"}]}, {"Route": "images/products/644ecd1b-db4d-48ae-97a0-0daed369fd66_بخور.tobjvbqvfq.webp", "AssetFile": "images/products/644ecd1b-db4d-48ae-97a0-0daed369fd66_بخور.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "52850"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"E1M68AyUnq/AIV63aOJ080O46NEvUZwdChZfUBgIQ78=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:14:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tobjvbqvfq"}, {"Name": "integrity", "Value": "sha256-E1M68AyUnq/AIV63aOJ080O46NEvUZwdChZfUBgIQ78="}, {"Name": "label", "Value": "images/products/644ecd1b-db4d-48ae-97a0-0daed369fd66_بخور.webp"}]}, {"Route": "images/products/644ecd1b-db4d-48ae-97a0-0daed369fd66_بخور.webp", "AssetFile": "images/products/644ecd1b-db4d-48ae-97a0-0daed369fd66_بخور.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "52850"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"E1M68AyUnq/AIV63aOJ080O46NEvUZwdChZfUBgIQ78=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Apr 2025 16:14:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E1M68AyUnq/AIV63aOJ080O46NEvUZwdChZfUBgIQ78="}]}, {"Route": "images/products/85ab999b-6638-4e8f-ac28-5dca640f749f_5cb40186-9166-4db9-b655-7786a33cd645.7lq2mv9ipm.jpg", "AssetFile": "images/products/85ab999b-6638-4e8f-ac28-5dca640f749f_5cb40186-9166-4db9-b655-7786a33cd645.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4622"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5UrHwA/aIi+0JaQaOmEVBYISYtzUFknL52La9uj/yjk=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:33:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7lq2mv9ipm"}, {"Name": "integrity", "Value": "sha256-5UrHwA/aIi+0JaQaOmEVBYISYtzUFknL52La9uj/yjk="}, {"Name": "label", "Value": "images/products/85ab999b-6638-4e8f-ac28-5dca640f749f_5cb40186-9166-4db9-b655-7786a33cd645.jpg"}]}, {"Route": "images/products/85ab999b-6638-4e8f-ac28-5dca640f749f_5cb40186-9166-4db9-b655-7786a33cd645.jpg", "AssetFile": "images/products/85ab999b-6638-4e8f-ac28-5dca640f749f_5cb40186-9166-4db9-b655-7786a33cd645.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "4622"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5UrHwA/aIi+0JaQaOmEVBYISYtzUFknL52La9uj/yjk=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:33:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5UrHwA/aIi+0JaQaOmEVBYISYtzUFknL52La9uj/yjk="}]}, {"Route": "images/products/9c490ac1-3d58-4268-9b36-e3e98fa732c0_Screenshot 2025-05-16 at 11.32.58 PM.hwg92hdddr.png", "AssetFile": "images/products/9c490ac1-3d58-4268-9b36-e3e98fa732c0_Screenshot 2025-05-16 at 11.32.58 PM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1331754"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"LwzrHLdBBjr6wdwxRP19SRxnRkKwmuFjlZCPRBwWPEY=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 19:46:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hwg92hdddr"}, {"Name": "integrity", "Value": "sha256-LwzrHLdBBjr6wdwxRP19SRxnRkKwmuFjlZCPRBwWPEY="}, {"Name": "label", "Value": "images/products/9c490ac1-3d58-4268-9b36-e3e98fa732c0_Screenshot 2025-05-16 at 11.32.58 PM.png"}]}, {"Route": "images/products/9c490ac1-3d58-4268-9b36-e3e98fa732c0_Screenshot 2025-05-16 at 11.32.58 PM.png", "AssetFile": "images/products/9c490ac1-3d58-4268-9b36-e3e98fa732c0_Screenshot 2025-05-16 at 11.32.58 PM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1331754"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"LwzrHLdBBjr6wdwxRP19SRxnRkKwmuFjlZCPRBwWPEY=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 19:46:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LwzrHLdBBjr6wdwxRP19SRxnRkKwmuFjlZCPRBwWPEY="}]}, {"Route": "images/products/a0c693c6-df1d-4693-9ce2-52932d1fc860_c2150034-bccf-4b42-a3b4-4fdb0e7a4cbd(1).2fc5im8l8b.jpg", "AssetFile": "images/products/a0c693c6-df1d-4693-9ce2-52932d1fc860_c2150034-bccf-4b42-a3b4-4fdb0e7a4cbd(1).jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "62894"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"HvhCu9SVknlYJFjMszT5CJ2jIrpFiLVzT2uqtubu8KM=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:34:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2fc5im8l8b"}, {"Name": "integrity", "Value": "sha256-HvhCu9SVknlYJFjMszT5CJ2jIrpFiLVzT2uqtubu8KM="}, {"Name": "label", "Value": "images/products/a0c693c6-df1d-4693-9ce2-52932d1fc860_c2150034-bccf-4b42-a3b4-4fdb0e7a4cbd(1).jpg"}]}, {"Route": "images/products/a0c693c6-df1d-4693-9ce2-52932d1fc860_c2150034-bccf-4b42-a3b4-4fdb0e7a4cbd(1).jpg", "AssetFile": "images/products/a0c693c6-df1d-4693-9ce2-52932d1fc860_c2150034-bccf-4b42-a3b4-4fdb0e7a4cbd(1).jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "62894"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"HvhCu9SVknlYJFjMszT5CJ2jIrpFiLVzT2uqtubu8KM=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:34:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HvhCu9SVknlYJFjMszT5CJ2jIrpFiLVzT2uqtubu8KM="}]}, {"Route": "images/products/bad3f1a8-fec5-41bd-9f4f-b5604eeb9a4f_m4.gsbsxni7t2.jpg", "AssetFile": "images/products/bad3f1a8-fec5-41bd-9f4f-b5604eeb9a4f_m4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "285395"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UHjxYFgaZWqcnt4CWI8986fJnQmUF455dX3DzfSyZyE=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:32:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gsbsxni7t2"}, {"Name": "integrity", "Value": "sha256-UHjxYFgaZWqcnt4CWI8986fJnQmUF455dX3DzfSyZyE="}, {"Name": "label", "Value": "images/products/bad3f1a8-fec5-41bd-9f4f-b5604eeb9a4f_m4.jpg"}]}, {"Route": "images/products/bad3f1a8-fec5-41bd-9f4f-b5604eeb9a4f_m4.jpg", "AssetFile": "images/products/bad3f1a8-fec5-41bd-9f4f-b5604eeb9a4f_m4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "285395"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UHjxYFgaZWqcnt4CWI8986fJnQmUF455dX3DzfSyZyE=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:32:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UHjxYFgaZWqcnt4CWI8986fJnQmUF455dX3DzfSyZyE="}]}, {"Route": "images/products/c742f74d-12b7-4cc5-b91c-6ad46739562b_ac2.izpqy7jh2z.webp", "AssetFile": "images/products/c742f74d-12b7-4cc5-b91c-6ad46739562b_ac2.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "33794"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:33:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "izpqy7jh2z"}, {"Name": "integrity", "Value": "sha256-DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0="}, {"Name": "label", "Value": "images/products/c742f74d-12b7-4cc5-b91c-6ad46739562b_ac2.webp"}]}, {"Route": "images/products/c742f74d-12b7-4cc5-b91c-6ad46739562b_ac2.webp", "AssetFile": "images/products/c742f74d-12b7-4cc5-b91c-6ad46739562b_ac2.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "33794"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:33:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0="}]}, {"Route": "images/products/d0cc7e14-260e-4c2d-a86a-18c37e8ff574_6e5a6f21a01711704e66278e4bece5e2_1736393104268.5jynjgbyyo.jpg", "AssetFile": "images/products/d0cc7e14-260e-4c2d-a86a-18c37e8ff574_6e5a6f21a01711704e66278e4bece5e2_1736393104268.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32284"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b45hK0tZ6/CSXN4Ur5Hk76a1VDdtI58VoxhGs3pUvGA=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:34:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5jynjgbyyo"}, {"Name": "integrity", "Value": "sha256-b45hK0tZ6/CSXN4Ur5Hk76a1VDdtI58VoxhGs3pUvGA="}, {"Name": "label", "Value": "images/products/d0cc7e14-260e-4c2d-a86a-18c37e8ff574_6e5a6f21a01711704e66278e4bece5e2_1736393104268.jpg"}]}, {"Route": "images/products/d0cc7e14-260e-4c2d-a86a-18c37e8ff574_6e5a6f21a01711704e66278e4bece5e2_1736393104268.jpg", "AssetFile": "images/products/d0cc7e14-260e-4c2d-a86a-18c37e8ff574_6e5a6f21a01711704e66278e4bece5e2_1736393104268.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "32284"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b45hK0tZ6/CSXN4Ur5Hk76a1VDdtI58VoxhGs3pUvGA=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:34:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b45hK0tZ6/CSXN4Ur5Hk76a1VDdtI58VoxhGs3pUvGA="}]}, {"Route": "images/products/f075d19a-40fa-4f11-a31b-52fbf438caa9_fbbdaf893ee54edb9c1b29c7c0e87da3-goods.jpeg", "AssetFile": "images/products/f075d19a-40fa-4f11-a31b-52fbf438caa9_fbbdaf893ee54edb9c1b29c7c0e87da3-goods.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "16714"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"KE6Ilxiy5SL14d+yDtxFBOPX65abkTt29JUdFbQxUwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:33:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KE6Ilxiy5SL14d+yDtxFBOPX65abkTt29JUdFbQxUwg="}]}, {"Route": "images/products/f075d19a-40fa-4f11-a31b-52fbf438caa9_fbbdaf893ee54edb9c1b29c7c0e87da3-goods.kxigy79ufi.jpeg", "AssetFile": "images/products/f075d19a-40fa-4f11-a31b-52fbf438caa9_fbbdaf893ee54edb9c1b29c7c0e87da3-goods.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16714"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"KE6Ilxiy5SL14d+yDtxFBOPX65abkTt29JUdFbQxUwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Apr 2025 11:33:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kxigy79ufi"}, {"Name": "integrity", "Value": "sha256-KE6Ilxiy5SL14d+yDtxFBOPX65abkTt29JUdFbQxUwg="}, {"Name": "label", "Value": "images/products/f075d19a-40fa-4f11-a31b-52fbf438caa9_fbbdaf893ee54edb9c1b29c7c0e87da3-goods.jpeg"}]}, {"Route": "js/active-link.g946x0afaq.js", "AssetFile": "js/active-link.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4326"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7IOPDDGzpx2eKPvR8T98Mbxy63ipdTq0EPrDEjIiuxE=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 18:19:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g946x0afaq"}, {"Name": "integrity", "Value": "sha256-7IOPDDGzpx2eKPvR8T98Mbxy63ipdTq0EPrDEjIiuxE="}, {"Name": "label", "Value": "js/active-link.js"}]}, {"Route": "js/active-link.js", "AssetFile": "js/active-link.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4326"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7IOPDDGzpx2eKPvR8T98Mbxy63ipdTq0EPrDEjIiuxE=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 18:19:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7IOPDDGzpx2eKPvR8T98Mbxy63ipdTq0EPrDEjIiuxE="}]}, {"Route": "js/carousel-form.js", "AssetFile": "js/carousel-form.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1970"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XebfI25KNkkvozuDE2Y71Jrkg31odAhprmy3pXgURZk=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:13:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XebfI25KNkkvozuDE2Y71Jrkg31odAhprmy3pXgURZk="}]}, {"Route": "js/carousel-form.luhwojk1w7.js", "AssetFile": "js/carousel-form.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1970"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XebfI25KNkkvozuDE2Y71Jrkg31odAhprmy3pXgURZk=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:13:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "luhwojk1w7"}, {"Name": "integrity", "Value": "sha256-XebfI25KNkkvozuDE2Y71Jrkg31odAhprmy3pXgURZk="}, {"Name": "label", "Value": "js/carousel-form.js"}]}, {"Route": "js/disable-console-updates.js", "AssetFile": "js/disable-console-updates.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2716"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cYrZFiU3Vzt5fyXXiX+TwgUcaEIZLax63arjyY33nfs=\""}, {"Name": "Last-Modified", "Value": "Sat, 17 May 2025 19:10:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cYrZFiU3Vzt5fyXXiX+TwgUcaEIZLax63arjyY33nfs="}]}, {"Route": "js/disable-console-updates.lfsuv0u95o.js", "AssetFile": "js/disable-console-updates.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2716"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cYrZFiU3Vzt5fyXXiX+TwgUcaEIZLax63arjyY33nfs=\""}, {"Name": "Last-Modified", "Value": "Sat, 17 May 2025 19:10:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lfsuv0u95o"}, {"Name": "integrity", "Value": "sha256-cYrZFiU3Vzt5fyXXiX+TwgUcaEIZLax63arjyY33nfs="}, {"Name": "label", "Value": "js/disable-console-updates.js"}]}, {"Route": "js/display-options.js", "AssetFile": "js/display-options.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DVgC6HPLFIH3ytv3N4mwkFXPC3tDxIfD6pVaf5YKqeg=\""}, {"Name": "Last-Modified", "Value": "Sat, 17 May 2025 18:57:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DVgC6HPLFIH3ytv3N4mwkFXPC3tDxIfD6pVaf5YKqeg="}]}, {"Route": "js/display-options.n3rbmlgo3i.js", "AssetFile": "js/display-options.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7460"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DVgC6HPLFIH3ytv3N4mwkFXPC3tDxIfD6pVaf5YKqeg=\""}, {"Name": "Last-Modified", "Value": "Sat, 17 May 2025 18:57:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n3rbmlgo3i"}, {"Name": "integrity", "Value": "sha256-DVgC6HPLFIH3ytv3N4mwkFXPC3tDxIfD6pVaf5YKqeg="}, {"Name": "label", "Value": "js/display-options.js"}]}, {"Route": "js/login.ggrgqbk8c3.js", "AssetFile": "js/login.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2665"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AJUyKWjmckdA0HSy1UD3m2zWtaoeG1QFHg3G1EgZbfg=\""}, {"Name": "Last-Modified", "Value": "Fri, 09 May 2025 11:44:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ggrgqbk8c3"}, {"Name": "integrity", "Value": "sha256-AJUyKWjmckdA0HSy1UD3m2zWtaoeG1QFHg3G1EgZbfg="}, {"Name": "label", "Value": "js/login.js"}]}, {"Route": "js/login.js", "AssetFile": "js/login.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2665"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AJUyKWjmckdA0HSy1UD3m2zWtaoeG1QFHg3G1EgZbfg=\""}, {"Name": "Last-Modified", "Value": "Fri, 09 May 2025 11:44:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AJUyKWjmckdA0HSy1UD3m2zWtaoeG1QFHg3G1EgZbfg="}]}, {"Route": "js/mobile-sidebar.js", "AssetFile": "js/mobile-sidebar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2279"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y7r68BpT6rhFq/Ex5Cxz73YlWLiGilGekfe0LnA+Zg8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 19:21:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y7r68BpT6rhFq/Ex5Cxz73YlWLiGilGekfe0LnA+Zg8="}]}, {"Route": "js/mobile-sidebar.z2251kn5va.js", "AssetFile": "js/mobile-sidebar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2279"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y7r68BpT6rhFq/Ex5Cxz73YlWLiGilGekfe0LnA+Zg8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 19:21:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z2251kn5va"}, {"Name": "integrity", "Value": "sha256-y7r68BpT6rhFq/Ex5Cxz73YlWLiGilGekfe0LnA+Zg8="}, {"Name": "label", "Value": "js/mobile-sidebar.js"}]}, {"Route": "js/modal-fix.cdbh4265fs.js", "AssetFile": "js/modal-fix.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2679"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MEAXM/n/+kH90cwO4Y2LZ4AF1Ys8J8thp0wjNm4JHx4=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 21:56:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cdbh4265fs"}, {"Name": "integrity", "Value": "sha256-MEAXM/n/+kH90cwO4Y2LZ4AF1Ys8J8thp0wjNm4JHx4="}, {"Name": "label", "Value": "js/modal-fix.js"}]}, {"Route": "js/modal-fix.js", "AssetFile": "js/modal-fix.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2679"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MEAXM/n/+kH90cwO4Y2LZ4AF1Ys8J8thp0wjNm4JHx4=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 21:56:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MEAXM/n/+kH90cwO4Y2LZ4AF1Ys8J8thp0wjNm4JHx4="}]}, {"Route": "js/product-actions.7vaboflnow.js", "AssetFile": "js/product-actions.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "62901"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"w8+6qVw5nxhwrWqVNnvVLZM5t8HguCVshgBZ8zMERkU=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:17:10 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7vaboflnow"}, {"Name": "integrity", "Value": "sha256-w8+6qVw5nxhwrWqVNnvVLZM5t8HguCVshgBZ8zMERkU="}, {"Name": "label", "Value": "js/product-actions.js"}]}, {"Route": "js/product-actions.js", "AssetFile": "js/product-actions.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "62901"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"w8+6qVw5nxhwrWqVNnvVLZM5t8HguCVshgBZ8zMERkU=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:17:10 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-w8+6qVw5nxhwrWqVNnvVLZM5t8HguCVshgBZ8zMERkU="}]}, {"Route": "js/remove-second-navbar.js", "AssetFile": "js/remove-second-navbar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1918"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gPtPDge+er26C39K+MPH75TvhOTMiKKuVDmBQA+kHqg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gPtPDge+er26C39K+MPH75TvhOTMiKKuVDmBQA+kHqg="}]}, {"Route": "js/remove-second-navbar.kbe08x5ltw.js", "AssetFile": "js/remove-second-navbar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1918"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gPtPDge+er26C39K+MPH75TvhOTMiKKuVDmBQA+kHqg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbe08x5ltw"}, {"Name": "integrity", "Value": "sha256-gPtPDge+er26C39K+MPH75TvhOTMiKKuVDmBQA+kHqg="}, {"Name": "label", "Value": "js/remove-second-navbar.js"}]}, {"Route": "js/remove-secondary-nav.js", "AssetFile": "js/remove-secondary-nav.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1817"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r3WfnbhIvY00RIS0Ddr1J0/cSFxbyx3WBo16Unz1E/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:04:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r3WfnbhIvY00RIS0Ddr1J0/cSFxbyx3WBo16Unz1E/4="}]}, {"Route": "js/remove-secondary-nav.vkwju3khpq.js", "AssetFile": "js/remove-secondary-nav.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1817"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r3WfnbhIvY00RIS0Ddr1J0/cSFxbyx3WBo16Unz1E/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:04:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vkwju3khpq"}, {"Name": "integrity", "Value": "sha256-r3WfnbhIvY00RIS0Ddr1J0/cSFxbyx3WBo16Unz1E/4="}, {"Name": "label", "Value": "js/remove-secondary-nav.js"}]}, {"Route": "js/remove-specific-navbar.8qxd3tlql5.js", "AssetFile": "js/remove-specific-navbar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2936"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ADNjbA/G0Vg1bnm15q11jD8yi9MMQjiqYYlZZipXtqE=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:04:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8qxd3tlql5"}, {"Name": "integrity", "Value": "sha256-ADNjbA/G0Vg1bnm15q11jD8yi9MMQjiqYYlZZipXtqE="}, {"Name": "label", "Value": "js/remove-specific-navbar.js"}]}, {"Route": "js/remove-specific-navbar.js", "AssetFile": "js/remove-specific-navbar.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2936"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ADNjbA/G0Vg1bnm15q11jD8yi9MMQjiqYYlZZipXtqE=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:04:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ADNjbA/G0Vg1bnm15q11jD8yi9MMQjiqYYlZZipXtqE="}]}, {"Route": "js/side-filter.empg2p9s04.js", "AssetFile": "js/side-filter.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10928"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"trt/wO9a98hlPS1lmphfjlOO3zb+eIG3uwnRjMQ86yI=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:09:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "empg2p9s04"}, {"Name": "integrity", "Value": "sha256-trt/wO9a98hlPS1lmphfjlOO3zb+eIG3uwnRjMQ86yI="}, {"Name": "label", "Value": "js/side-filter.js"}]}, {"Route": "js/side-filter.js", "AssetFile": "js/side-filter.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10928"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"trt/wO9a98hlPS1lmphfjlOO3zb+eIG3uwnRjMQ86yI=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 20:09:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-trt/wO9a98hlPS1lmphfjlOO3zb+eIG3uwnRjMQ86yI="}]}, {"Route": "js/side-login.js", "AssetFile": "js/side-login.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5842"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+Bdg6nx3S0f6jBT2cllYCSANCDZoU7cB8KCFA84a54U=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 18:20:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Bdg6nx3S0f6jBT2cllYCSANCDZoU7cB8KCFA84a54U="}]}, {"Route": "js/side-login.oo1mqne9y7.js", "AssetFile": "js/side-login.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5842"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+Bdg6nx3S0f6jBT2cllYCSANCDZoU7cB8KCFA84a54U=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 18:20:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oo1mqne9y7"}, {"Name": "integrity", "Value": "sha256-+Bdg6nx3S0f6jBT2cllYCSANCDZoU7cB8KCFA84a54U="}, {"Name": "label", "Value": "js/side-login.js"}]}, {"Route": "js/site.9hzxeb87ay.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17709"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3UuyYKrjOhkwFq3Q8/GylSQtar8APsUtSkPQqz7sr3Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:15:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9hzxeb87ay"}, {"Name": "integrity", "Value": "sha256-3UuyYKrjOhkwFq3Q8/GylSQtar8APsUtSkPQqz7sr3Q="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17709"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3UuyYKrjOhkwFq3Q8/GylSQtar8APsUtSkPQqz7sr3Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:15:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3UuyYKrjOhkwFq3Q8/GylSQtar8APsUtSkPQqz7sr3Q="}]}, {"Route": "js/sticky-nav-active.50s1b9gnhy.js", "AssetFile": "js/sticky-nav-active.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5400"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BbOMAy1hgF94ylMXvzv3w57rJ89JE3JyH4aISaIVB0I=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:03:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "50s1b9gnhy"}, {"Name": "integrity", "Value": "sha256-BbOMAy1hgF94ylMXvzv3w57rJ89JE3JyH4aISaIVB0I="}, {"Name": "label", "Value": "js/sticky-nav-active.js"}]}, {"Route": "js/sticky-nav-active.js", "AssetFile": "js/sticky-nav-active.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5400"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BbOMAy1hgF94ylMXvzv3w57rJ89JE3JyH4aISaIVB0I=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 14:03:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BbOMAy1hgF94ylMXvzv3w57rJ89JE3JyH4aISaIVB0I="}]}, {"Route": "js/sticky-nav.js", "AssetFile": "js/sticky-nav.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3760"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LGYTFSWXk0c/5NZ5+wZXkR7pNR/+XTvAEW+kk0CRRrc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 13:48:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LGYTFSWXk0c/5NZ5+wZXkR7pNR/+XTvAEW+kk0CRRrc="}]}, {"Route": "js/sticky-nav.sg9539kvw7.js", "AssetFile": "js/sticky-nav.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3760"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LGYTFSWXk0c/5NZ5+wZXkR7pNR/+XTvAEW+kk0CRRrc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 13:48:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sg9539kvw7"}, {"Name": "integrity", "Value": "sha256-LGYTFSWXk0c/5NZ5+wZXkR7pNR/+XTvAEW+kk0CRRrc="}, {"Name": "label", "Value": "js/sticky-nav.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.48y08845bh.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5831"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RFWFWIIPsjB4DucR4jqwxTWw13ZmtI+s6tVR2LJmZXk=\""}, {"Name": "Last-Modified", "Value": "Thu, 15 May 2025 20:18:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48y08845bh"}, {"Name": "integrity", "Value": "sha256-RFWFWIIPsjB4DucR4jqwxTWw13ZmtI+s6tVR2LJmZXk="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5831"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RFWFWIIPsjB4DucR4jqwxTWw13ZmtI+s6tVR2LJmZXk=\""}, {"Name": "Last-Modified", "Value": "Thu, 15 May 2025 20:18:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RFWFWIIPsjB4DucR4jqwxTWw13ZmtI+s6tVR2LJmZXk="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Jul 2022 11:19:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.b7iojwaux1.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Thu, 15 May 2025 20:18:08 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7iojwaux1"}, {"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Thu, 15 May 2025 20:18:08 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.pzqfkb6aqo.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Jul 2022 11:19:40 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pzqfkb6aqo"}, {"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}]}, {"Route": "lib/jquery-validation/jquery.validate.min.b7iojwaux1.js", "AssetFile": "lib/jquery-validation/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Jul 2022 11:19:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7iojwaux1"}, {"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}, {"Name": "label", "Value": "lib/jquery-validation/jquery.validate.min.js"}]}, {"Route": "lib/jquery-validation/jquery.validate.min.js", "AssetFile": "lib/jquery-validation/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Fri, 01 Jul 2022 11:19:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "upload-carousel.html", "AssetFile": "upload-carousel.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8605"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Frz5pVerTi1e3MZdabCrxaK69XAQ0gxYcWpU0LbFqlk=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:03:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Frz5pVerTi1e3MZdabCrxaK69XAQ0gxYcWpU0LbFqlk="}]}, {"Route": "upload-carousel.mcdaagtskk.html", "AssetFile": "upload-carousel.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8605"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Frz5pVerTi1e3MZdabCrxaK69XAQ0gxYcWpU0LbFqlk=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 21:03:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mcdaagtskk"}, {"Name": "integrity", "Value": "sha256-Frz5pVerTi1e3MZdabCrxaK69XAQ0gxYcWpU0LbFqlk="}, {"Name": "label", "Value": "upload-carousel.html"}]}]}