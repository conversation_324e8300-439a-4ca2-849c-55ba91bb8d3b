﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\admin-dashboard.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/admin-dashboard.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>8uatryui52</Fingerprint>
      <Integrity>IP8jIYZUDK44JjkCXHRAloarW9wtDbQJR8LEUpmidVk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\admin-dashboard.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\animations.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/animations.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>urkx21zm8o</Fingerprint>
      <Integrity>an3mPfJv/bxSKqLaeLUZ3fCNmqyoExZML/8N/H8U3aw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\animations.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\badge-position-fix.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/badge-position-fix.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>htl75v7fof</Fingerprint>
      <Integrity>NZuSQcu4Kpt8q0NQkqkf008A59zV93KT5IXu4lL72IQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\badge-position-fix.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\carousel-custom.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/carousel-custom.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4axo0o8gb8</Fingerprint>
      <Integrity>hGE3TWl542v8W08W4k79YO2jtAI/TF6iRJP5hJ2Pc30=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\carousel-custom.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\cart.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/cart.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>bjxwgamrx0</Fingerprint>
      <Integrity>xwmZQ2chIF4F0v7jwU5ZkN5VRPJ95y8nZ0Cc/yg94Ho=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\cart.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\display-options.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/display-options.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vn2lmwmp1g</Fingerprint>
      <Integrity>RQlYWM3DR9OTj5RoEzxMStTvIQ/IxE8SkJJtZcJeX4U=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\display-options.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\header-ameera.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/header-ameera.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>88e0k1mrs5</Fingerprint>
      <Integrity>CKDXaqU87kdWZ8sjVPx0w+T6RwggGud9nFTQ4n9wvmM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\header-ameera.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\header-icons.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/header-icons.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>g7pj4cazn1</Fingerprint>
      <Integrity>XJooe1e+oSE50LN2bS8anCw9Qz4hExKUxa44zyyd2fA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\header-icons.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\hide-secondary-nav.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/hide-secondary-nav.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>cq41qncp2j</Fingerprint>
      <Integrity>NOQW3UVvdDog75K/GQHk0sRAxw7qC0h6pXrjR45FOY8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\hide-secondary-nav.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\login-register-buttons.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/login-register-buttons.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>x8klhfpnk9</Fingerprint>
      <Integrity>wfbatODrUwcPfx/QzWPxwbo3u3SGiWxDAr6BuHp2QAA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\login-register-buttons.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\mobile-header.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/mobile-header.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>d8wl7mhfgc</Fingerprint>
      <Integrity>kxqdJFBrZYaCaXr+hWw45LEKpXnmomnNOn9FUFO5szw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\mobile-header.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\mobile-menu.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/mobile-menu.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>wg1xii7wok</Fingerprint>
      <Integrity>UDCSqNw9nDaSP0NgRE9UEJ+rezycoCrYsZFKuFc5JrY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\mobile-menu.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\mobile-nav.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/mobile-nav.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>y3tt5r0bfr</Fingerprint>
      <Integrity>YpajnkANtQL4ueT7NAW2EqdMYnW3fqgbRgcLklojX5M=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\mobile-nav.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\navbar-custom.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/navbar-custom.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>5zo4emlyzu</Fingerprint>
      <Integrity>j/KPq8kBnGyy+cTBmD++fgL3FB23KIFBTm7h8vEVwG8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\navbar-custom.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\navbar-font.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/navbar-font.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>6v0jesy39u</Fingerprint>
      <Integrity>3tblWIWQaWf7aLyTtUWjjihdln3c+Fark7mlrCrj3JQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\navbar-font.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\order-actions.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/order-actions.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>robqrlnlsf</Fingerprint>
      <Integrity>VSN8AKyHp9+t5fwJZLueFRg8nCglOPHz3acX3nM/Ytg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\order-actions.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\order-tracking.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/order-tracking.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>9eddqbsaju</Fingerprint>
      <Integrity>79RXGn0d+rK111p9AZL3fW6IaAOSAwdPq2LFgCvZ2R4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\order-tracking.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\product-display.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/product-display.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>p1dayp1wiq</Fingerprint>
      <Integrity>cyl2cxMs3fqp9doLWantA30/A1PgezKE+zDzB8rF9RA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\product-display.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\products-page.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/products-page.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vnq2fk2oz9</Fingerprint>
      <Integrity>e1LSd5vFxGIeAiPWOCHy4+tsomqkQ/L8dLPWn3/6XuU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\products-page.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\remove-second-navbar.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/remove-second-navbar.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>a47mkjn00m</Fingerprint>
      <Integrity>dgLjOD6Oym7UH2DELtt8988x7kL5I3h+Cp2jwVc6VVI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\remove-second-navbar.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\reverse-navbar-items.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/reverse-navbar-items.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rel69c2ik9</Fingerprint>
      <Integrity>46rBl88bCFRVi9fwv/bppDnuNH/s9BLYHPGLYVZEQo8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\reverse-navbar-items.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\rtl-fixes.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/rtl-fixes.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3su50rba47</Fingerprint>
      <Integrity>7Vg4vQPJ2BaTR00DX+hDC1afgS7MbtYq0JVENNaLFCQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\rtl-fixes.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\side-filter.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/side-filter.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ulwnuo15fi</Fingerprint>
      <Integrity>Lm1h3MZt5cHSfCYIXhjXfOUZQCFSBla8lxfeUlz/SdQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\side-filter.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\side-login.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/side-login.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>8kysxfx9q0</Fingerprint>
      <Integrity>CLUS1ZR+PI6oF4az5C04Z9oEmZLha4togB9B6wlnR0g=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\side-login.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/site.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3eo8uectje</Fingerprint>
      <Integrity>ewOJiPE97j9y3eGT1EhRibmI+3NumNrRyNdS3Onj05w=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\sticky-nav.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/sticky-nav.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>d54w1ogls8</Fingerprint>
      <Integrity>AR7o9zPcaO4tmUH7Qzzda+65W9pSi0iDjEFnTam5uyY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\sticky-nav.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\wishlist-compare.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>css/wishlist-compare.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>5jwntsrdzt</Fingerprint>
      <Integrity>j97GgdnFycH4dAH/P+0hsRBgJRoQ9czpbRK98p1AdqI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\wishlist-compare.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\404-illustration.svg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/404-illustration.svg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>i5zppwvnep</Fingerprint>
      <Integrity>NmcErXMyaZp/gc6UAISlrwSrdgzBwSz2zJR061v7hjI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\404-illustration.svg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a1.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/a1.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>tbx6dtwmmi</Fingerprint>
      <Integrity>OS9QfO/W2WhI3rzWGaYMiPaalJo3kgGuGo4b2EYNLec=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a1.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a2.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/a2.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2fatrhwnx5</Fingerprint>
      <Integrity>/ngMMLu5aX5mUqCbaqGAZVtAlt6AWHXHnPpHzczajJI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a2.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a3.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/a3.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>bsqb6wrswg</Fingerprint>
      <Integrity>W1JcXPgva9Yor9uZ5m34vGVRooSSvWjvNXNA0X2PLB0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a3.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a4.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/a4.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r4gocgcpny</Fingerprint>
      <Integrity>VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a4.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b1.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/b1.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>a7z62bf56t</Fingerprint>
      <Integrity>Sopf4V4URPLlYBjZigLyx30EXo6gAcuqe6OC73Da0Vg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b1.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b2.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/b2.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3zp5v47pzk</Fingerprint>
      <Integrity>v9HJ+NVOwCw6+tpBKaVXXRJ86YgAWEXtz0s1KCZ2jY8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b2.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b3.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/b3.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qsto1bq0yv</Fingerprint>
      <Integrity>2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b3.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b4.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/b4.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2g0hd85nwd</Fingerprint>
      <Integrity>foYah1MgaKP2ON+de85ANMHs/9Bd4zR1bnbBkuXL1+I=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b4.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c1.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/c1.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>g0xjhzgmw4</Fingerprint>
      <Integrity>IDVPC44xIiLfs//PgMdsI9wN8Lz+qRJnm1urFNlel9E=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c1.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c2.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/c2.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3dnytqrkhm</Fingerprint>
      <Integrity>xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c2.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c3.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/c3.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ngrnkhdbet</Fingerprint>
      <Integrity>OQ2Hc8A/REDbeHlR7p6D2QrcH3VpiEvXp4o+6yrmQw4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c3.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c3.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/c3.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>aqmotzmrya</Fingerprint>
      <Integrity>ElLT8JzH8vpts5n+lUSCopOSZPsNJZIGmvtzrBlECWg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c3.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c5.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/c5.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>inuyjl9xgq</Fingerprint>
      <Integrity>UvlKbAtMn/qZLz2MJ7XIkMmHWKIeUwoHyDSEQMpXj+w=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\c5.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\carousel\0d7440a8-7cb9-4602-9df0-9a88263652ca_بخور٢.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/carousel/0d7440a8-7cb9-4602-9df0-9a88263652ca_بخور٢.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>n0w87jnoql</Fingerprint>
      <Integrity>6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\carousel\0d7440a8-7cb9-4602-9df0-9a88263652ca_بخور٢.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\carousel\ab507d8b-4575-439b-b9a3-4662256d6169_Screenshot 2025-05-17 at 1.34.25 AM.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/carousel/ab507d8b-4575-439b-b9a3-4662256d6169_Screenshot 2025-05-17 at 1.34.25 AM.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>glz7k71yic</Fingerprint>
      <Integrity>9E/m/SfrbM9YdBUIR5ryPRXNZu0s4lcKG+4JG+BId6k=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\carousel\ab507d8b-4575-439b-b9a3-4662256d6169_Screenshot 2025-05-17 at 1.34.25 AM.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\carousel\ba59274b-8c4a-4c20-a1ba-dd13b2509a2b_Screenshot 2025-05-17 at 12.41.30 AM.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/carousel/ba59274b-8c4a-4c20-a1ba-dd13b2509a2b_Screenshot 2025-05-17 at 12.41.30 AM.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ay5ug7e3sh</Fingerprint>
      <Integrity>XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\carousel\ba59274b-8c4a-4c20-a1ba-dd13b2509a2b_Screenshot 2025-05-17 at 12.41.30 AM.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\4ef5438e-5c40-4ce4-a993-a619e3e8e823_0fc3c635-3118-4619-b3a7-44c4fc0115b5.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/4ef5438e-5c40-4ce4-a993-a619e3e8e823_0fc3c635-3118-4619-b3a7-44c4fc0115b5.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>iyou7fbwtc</Fingerprint>
      <Integrity>JnOsmPx1G4/sRMQH7hpjjcEqtYJgL2jXH6BghWONwZ4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\4ef5438e-5c40-4ce4-a993-a619e3e8e823_0fc3c635-3118-4619-b3a7-44c4fc0115b5.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\52343ccc-d7da-4d5d-b30b-c72235c19272_b3.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/52343ccc-d7da-4d5d-b30b-c72235c19272_b3.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qsto1bq0yv</Fingerprint>
      <Integrity>2vl1ooLNNRI7VA/dsKaqqoaH8zbScQBZJlgsau2S3Nw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\52343ccc-d7da-4d5d-b30b-c72235c19272_b3.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\631bcb87-3c37-49e2-8ade-aeab2f2eca2a_بخور٢.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/631bcb87-3c37-49e2-8ade-aeab2f2eca2a_بخور٢.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>n0w87jnoql</Fingerprint>
      <Integrity>6ah9MdKjXC6/N3YMe/uAlbSv5RAoif0k/2WiIKb+ytI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\631bcb87-3c37-49e2-8ade-aeab2f2eca2a_بخور٢.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\6f0b6ee4-75aa-4168-9867-4a87c4c3f658_a4.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/6f0b6ee4-75aa-4168-9867-4a87c4c3f658_a4.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r4gocgcpny</Fingerprint>
      <Integrity>VcyYrgeyIE/yYRZruYf8o3K1JJ1AmgTjQTVIZSY6EZM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\6f0b6ee4-75aa-4168-9867-4a87c4c3f658_a4.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\7d0c1833-918b-4f7a-b844-63cb9a2634e7_ab4.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/7d0c1833-918b-4f7a-b844-63cb9a2634e7_ab4.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3hub1du3uj</Fingerprint>
      <Integrity>BwSlGAxwXFMRI9gFEFuvQuaea4/SHpUrbVkA+CQ1jj4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\7d0c1833-918b-4f7a-b844-63cb9a2634e7_ab4.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\abayas-icon.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/abayas-icon.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2nms02u0vg</Fingerprint>
      <Integrity>nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\abayas-icon.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\accessories-icon.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/accessories-icon.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2nms02u0vg</Fingerprint>
      <Integrity>nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\accessories-icon.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\b980ccb8-4102-4b08-ba8d-3dbfe857d655_ac2.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/b980ccb8-4102-4b08-ba8d-3dbfe857d655_ac2.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>izpqy7jh2z</Fingerprint>
      <Integrity>DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\b980ccb8-4102-4b08-ba8d-3dbfe857d655_ac2.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\cc1f788a-3bfa-4df9-8c04-9fb9879671cd_hqdefault.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/cc1f788a-3bfa-4df9-8c04-9fb9879671cd_hqdefault.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>1dh8d89gid</Fingerprint>
      <Integrity>5eYpq6c5hXJTLrX8YL529gIFS1KGHdhjJoz/5uUXrds=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\cc1f788a-3bfa-4df9-8c04-9fb9879671cd_hqdefault.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\df2817c6-cb8f-4e86-8951-39f14b8bf383_m1.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/df2817c6-cb8f-4e86-8951-39f14b8bf383_m1.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>jqkg7lmamd</Fingerprint>
      <Integrity>+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\df2817c6-cb8f-4e86-8951-39f14b8bf383_m1.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\f87c55a3-d4bd-4cac-b0ff-05e79da04a06_c2.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/f87c55a3-d4bd-4cac-b0ff-05e79da04a06_c2.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3dnytqrkhm</Fingerprint>
      <Integrity>xz/BDcpaqExzLRd1s9zwSGg5/+SnyRww4A0GrivuHIw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\f87c55a3-d4bd-4cac-b0ff-05e79da04a06_c2.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\fabrics-icon.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/fabrics-icon.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2nms02u0vg</Fingerprint>
      <Integrity>nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\fabrics-icon.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\mukhwar-icon.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/categories/mukhwar-icon.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2nms02u0vg</Fingerprint>
      <Integrity>nonMPx3cP+N9vI6Iq1BaXxfzTmS/DKyBXGUyYrgQLoQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\categories\mukhwar-icon.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k1.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/k1.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vvfhzmc5z5</Fingerprint>
      <Integrity>q4VqF0WknT0mq7TeOqguxmrAMnxYioiUnp3/xuAPLII=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k1.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k2.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/k2.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>6o92510fz9</Fingerprint>
      <Integrity>dibprLVJx8JtWrC6q/fFBXDz8pMgD9zUda0Ah7XxFqs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k2.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k3.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/k3.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>v2n1h08d5h</Fingerprint>
      <Integrity>F5xvVOHYHsYmNmS71e2DG3gc7rNrYWIp3qU8M2MnmAM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k3.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k4.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/k4.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qneeprmq73</Fingerprint>
      <Integrity>StkE87xCr0mWdCv39oxwEyaC9bq2G2eEXXB7Wagjvao=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k4.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k5.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/k5.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qgp5kqv0kz</Fingerprint>
      <Integrity>1sODnvJtnoCABAyUPmuy75SMo7ddJGymhn8HP7lU1Yc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\k5.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/logo.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>7qm2e1160w</Fingerprint>
      <Integrity>j+GHIHsIUr4FsifVQixkpNPZjRsIjczo0ofW584Hyko=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo1.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/logo1.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>0dxnr7dp4p</Fingerprint>
      <Integrity>pI8fmqG6RcxP2hXx49t3H4lSAn/lfd9g0UkE1glkd88=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo1.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m1.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/m1.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>jqkg7lmamd</Fingerprint>
      <Integrity>+4eCI6FK79VxlEvTeWMe2HRVdDVKaAvF33Bsphy7/rw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m1.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m2.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/m2.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>6wj3vy2934</Fingerprint>
      <Integrity>itH33UhQ+Qb8BMkA+M/mOUcGwdbqqPhgVHw7Tzy8qFc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m2.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m3.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/m3.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>erd6xs7vi8</Fingerprint>
      <Integrity>1pN+5a9TkVPwO6r0VLMogCMsX7TnXwEpHWXLcqzKRus=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m3.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m4.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/m4.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>6slw65rivq</Fingerprint>
      <Integrity>up+b8OHbc9SAXZeThfVV14pmesLs2IBsIiKa/rVW3x8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m4.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m5.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/m5.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>57kkn2zbrn</Fingerprint>
      <Integrity>QVKkI5YFMookhXyAcbU/QdatyN7YBngrm8ZEMDhdOjc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\m5.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\placeholder-icon.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/placeholder-icon.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>e7b0ez3zfr</Fingerprint>
      <Integrity>Rp1JZf48zqecV94bcffxcW09Mcqbfo+K7yb9wdvI/j0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\placeholder-icon.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\0c9a3122-08ed-4c4b-8965-71f14a66a761_ab2.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/0c9a3122-08ed-4c4b-8965-71f14a66a761_ab2.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>9h36ung6xi</Fingerprint>
      <Integrity>445mFxos0f7GmLKop/+i6xvu1EaHXmfkCnLK3U2KpbM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\0c9a3122-08ed-4c4b-8965-71f14a66a761_ab2.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\19efcafc-f812-4039-a7b1-0854e98fa50b_Screenshot 2025-05-17 at 12.41.30 AM.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/19efcafc-f812-4039-a7b1-0854e98fa50b_Screenshot 2025-05-17 at 12.41.30 AM.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ay5ug7e3sh</Fingerprint>
      <Integrity>XlpI7RscSTLBLknOoQTJiFHUIwOwBQ0oOyXvAUXbrQ0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\19efcafc-f812-4039-a7b1-0854e98fa50b_Screenshot 2025-05-17 at 12.41.30 AM.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\262d4bcf-5ae7-4983-8e45-865a3f2c28b6_8ab8e7e9-97f6-4b02-86e5-10b8528758bb.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/262d4bcf-5ae7-4983-8e45-865a3f2c28b6_8ab8e7e9-97f6-4b02-86e5-10b8528758bb.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>p23v55vkc2</Fingerprint>
      <Integrity>UWrfufCGzWFWHdce4kCTA/3p+Df3QB2n8kLdKjWeltg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\262d4bcf-5ae7-4983-8e45-865a3f2c28b6_8ab8e7e9-97f6-4b02-86e5-10b8528758bb.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\644ecd1b-db4d-48ae-97a0-0daed369fd66_بخور.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/644ecd1b-db4d-48ae-97a0-0daed369fd66_بخور.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>tobjvbqvfq</Fingerprint>
      <Integrity>E1M68AyUnq/AIV63aOJ080O46NEvUZwdChZfUBgIQ78=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\644ecd1b-db4d-48ae-97a0-0daed369fd66_بخور.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\85ab999b-6638-4e8f-ac28-5dca640f749f_5cb40186-9166-4db9-b655-7786a33cd645.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/85ab999b-6638-4e8f-ac28-5dca640f749f_5cb40186-9166-4db9-b655-7786a33cd645.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>7lq2mv9ipm</Fingerprint>
      <Integrity>5UrHwA/aIi+0JaQaOmEVBYISYtzUFknL52La9uj/yjk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\85ab999b-6638-4e8f-ac28-5dca640f749f_5cb40186-9166-4db9-b655-7786a33cd645.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\9c490ac1-3d58-4268-9b36-e3e98fa732c0_Screenshot 2025-05-16 at 11.32.58 PM.png'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/9c490ac1-3d58-4268-9b36-e3e98fa732c0_Screenshot 2025-05-16 at 11.32.58 PM.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>hwg92hdddr</Fingerprint>
      <Integrity>LwzrHLdBBjr6wdwxRP19SRxnRkKwmuFjlZCPRBwWPEY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\9c490ac1-3d58-4268-9b36-e3e98fa732c0_Screenshot 2025-05-16 at 11.32.58 PM.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\a0c693c6-df1d-4693-9ce2-52932d1fc860_c2150034-bccf-4b42-a3b4-4fdb0e7a4cbd(1).jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/a0c693c6-df1d-4693-9ce2-52932d1fc860_c2150034-bccf-4b42-a3b4-4fdb0e7a4cbd(1).jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2fc5im8l8b</Fingerprint>
      <Integrity>HvhCu9SVknlYJFjMszT5CJ2jIrpFiLVzT2uqtubu8KM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\a0c693c6-df1d-4693-9ce2-52932d1fc860_c2150034-bccf-4b42-a3b4-4fdb0e7a4cbd(1).jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\bad3f1a8-fec5-41bd-9f4f-b5604eeb9a4f_m4.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/bad3f1a8-fec5-41bd-9f4f-b5604eeb9a4f_m4.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>gsbsxni7t2</Fingerprint>
      <Integrity>UHjxYFgaZWqcnt4CWI8986fJnQmUF455dX3DzfSyZyE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\bad3f1a8-fec5-41bd-9f4f-b5604eeb9a4f_m4.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\c742f74d-12b7-4cc5-b91c-6ad46739562b_ac2.webp'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/c742f74d-12b7-4cc5-b91c-6ad46739562b_ac2.webp</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>izpqy7jh2z</Fingerprint>
      <Integrity>DlTT4qP9dk1vphHUA++sjSJqVt2Fck5iOyVcN30HKi0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\c742f74d-12b7-4cc5-b91c-6ad46739562b_ac2.webp'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\d0cc7e14-260e-4c2d-a86a-18c37e8ff574_6e5a6f21a01711704e66278e4bece5e2_1736393104268.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/d0cc7e14-260e-4c2d-a86a-18c37e8ff574_6e5a6f21a01711704e66278e4bece5e2_1736393104268.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>5jynjgbyyo</Fingerprint>
      <Integrity>b45hK0tZ6/CSXN4Ur5Hk76a1VDdtI58VoxhGs3pUvGA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\d0cc7e14-260e-4c2d-a86a-18c37e8ff574_6e5a6f21a01711704e66278e4bece5e2_1736393104268.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\f075d19a-40fa-4f11-a31b-52fbf438caa9_fbbdaf893ee54edb9c1b29c7c0e87da3-goods.jpeg'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>images/products/f075d19a-40fa-4f11-a31b-52fbf438caa9_fbbdaf893ee54edb9c1b29c7c0e87da3-goods.jpeg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kxigy79ufi</Fingerprint>
      <Integrity>KE6Ilxiy5SL14d+yDtxFBOPX65abkTt29JUdFbQxUwg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\products\f075d19a-40fa-4f11-a31b-52fbf438caa9_fbbdaf893ee54edb9c1b29c7c0e87da3-goods.jpeg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\active-link.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/active-link.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>g946x0afaq</Fingerprint>
      <Integrity>7IOPDDGzpx2eKPvR8T98Mbxy63ipdTq0EPrDEjIiuxE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\active-link.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\carousel-form.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/carousel-form.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>luhwojk1w7</Fingerprint>
      <Integrity>XebfI25KNkkvozuDE2Y71Jrkg31odAhprmy3pXgURZk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\carousel-form.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\disable-console-updates.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/disable-console-updates.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>lfsuv0u95o</Fingerprint>
      <Integrity>cYrZFiU3Vzt5fyXXiX+TwgUcaEIZLax63arjyY33nfs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\disable-console-updates.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\display-options.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/display-options.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>n3rbmlgo3i</Fingerprint>
      <Integrity>DVgC6HPLFIH3ytv3N4mwkFXPC3tDxIfD6pVaf5YKqeg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\display-options.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\login.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/login.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ggrgqbk8c3</Fingerprint>
      <Integrity>AJUyKWjmckdA0HSy1UD3m2zWtaoeG1QFHg3G1EgZbfg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\login.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\mobile-sidebar.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/mobile-sidebar.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>z2251kn5va</Fingerprint>
      <Integrity>y7r68BpT6rhFq/Ex5Cxz73YlWLiGilGekfe0LnA+Zg8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\mobile-sidebar.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\modal-fix.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/modal-fix.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>cdbh4265fs</Fingerprint>
      <Integrity>MEAXM/n/+kH90cwO4Y2LZ4AF1Ys8J8thp0wjNm4JHx4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\modal-fix.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\product-actions.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/product-actions.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>szw3aijnv1</Fingerprint>
      <Integrity>yIu7esWnJXklK/uFAjy1gDxuHor1zTyP7QJ75vzTVDw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\product-actions.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\remove-second-navbar.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/remove-second-navbar.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kbe08x5ltw</Fingerprint>
      <Integrity>gPtPDge+er26C39K+MPH75TvhOTMiKKuVDmBQA+kHqg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\remove-second-navbar.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\remove-secondary-nav.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/remove-secondary-nav.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vkwju3khpq</Fingerprint>
      <Integrity>r3WfnbhIvY00RIS0Ddr1J0/cSFxbyx3WBo16Unz1E/4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\remove-secondary-nav.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\remove-specific-navbar.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/remove-specific-navbar.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>8qxd3tlql5</Fingerprint>
      <Integrity>ADNjbA/G0Vg1bnm15q11jD8yi9MMQjiqYYlZZipXtqE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\remove-specific-navbar.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\side-filter.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/side-filter.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>empg2p9s04</Fingerprint>
      <Integrity>trt/wO9a98hlPS1lmphfjlOO3zb+eIG3uwnRjMQ86yI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\side-filter.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\side-login.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/side-login.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>oo1mqne9y7</Fingerprint>
      <Integrity>+Bdg6nx3S0f6jBT2cllYCSANCDZoU7cB8KCFA84a54U=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\side-login.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/site.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r3vj6mnf81</Fingerprint>
      <Integrity>NzJ+chxIAvhnnjsZpNzIkK4kHIqlf3q+fkjZKYuoGqE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\sticky-nav.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/sticky-nav.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>sg9539kvw7</Fingerprint>
      <Integrity>LGYTFSWXk0c/5NZ5+wZXkR7pNR/+XTvAEW+kk0CRRrc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\sticky-nav.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\sticky-nav-active.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>js/sticky-nav-active.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>50s1b9gnhy</Fingerprint>
      <Integrity>BbOMAy1hgF94ylMXvzv3w57rJ89JE3JyH4aISaIVB0I=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\sticky-nav-active.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>48y08845bh</Fingerprint>
      <Integrity>RFWFWIIPsjB4DucR4jqwxTWw13ZmtI+s6tVR2LJmZXk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>lib/jquery-validation/dist/jquery.validate.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>pzqfkb6aqo</Fingerprint>
      <Integrity>m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>lib/jquery-validation/dist/jquery.validate.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>b7iojwaux1</Fingerprint>
      <Integrity>JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\jquery.validate.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>lib/jquery-validation/jquery.validate.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>b7iojwaux1</Fingerprint>
      <Integrity>JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\jquery.validate.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\upload-carousel.html'))">
      <SourceType>Package</SourceType>
      <SourceId>Abayat</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Abayat</BasePath>
      <RelativePath>upload-carousel.html</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>mcdaagtskk</Fingerprint>
      <Integrity>Frz5pVerTi1e3MZdabCrxaK69XAQ0gxYcWpU0LbFqlk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\upload-carousel.html'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>