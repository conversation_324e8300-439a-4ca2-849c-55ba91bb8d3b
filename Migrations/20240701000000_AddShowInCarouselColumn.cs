using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Abayat.Migrations
{
    public partial class AddShowInCarouselColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // التحقق من وجود العمود قبل إضافته
            migrationBuilder.Sql(@"
                IF NOT EXISTS (
                    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'ShowInCarousel'
                )
                BEGIN
                    ALTER TABLE Products
                    ADD ShowInCarousel BIT NOT NULL DEFAULT 0;
                END
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // التحقق من وجود العمود قبل حذفه
            migrationBuilder.Sql(@"
                IF EXISTS (
                    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'ShowInCarousel'
                )
                BEGIN
                    ALTER TABLE Products
                    DROP COLUMN ShowInCarousel;
                END
            ");
        }
    }
}
