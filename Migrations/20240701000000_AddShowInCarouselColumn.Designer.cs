// <auto-generated />
using System;
using Abayat.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Abayat.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20240701000000_AddShowInCarouselColumn")]
    partial class AddShowInCarouselColumn
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            // هنا يجب أن يكون نموذج قاعدة البيانات الكامل، لكن نحن نركز فقط على إضافة العمود
        }
    }
}
