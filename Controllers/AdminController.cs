using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Abayat.Data;
using Abayat.Models;
using Abayat.ViewModels;
using Abayat.Services;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;

namespace Abayat.Controllers
{
    [Authorize(Roles = "Admin")]
    public class AdminController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IOrderService _orderService;

        public AdminController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            IOrderService orderService)
        {
            _context = context;
            _userManager = userManager;
            _orderService = orderService;
        }

        // GET: Admin
        public async Task<IActionResult> Index()
        {
            // إحصائيات عامة
            var dashboardViewModel = new AdminDashboardViewModel
            {
                TotalProducts = await _context.Products.CountAsync(),
                TotalCategories = await _context.Categories.CountAsync(),
                TotalUsers = await _userManager.Users.CountAsync(),
                TotalOrders = await _context.Orders.CountAsync(),

                // الطلبات حسب الحالة
                PendingOrders = await _context.Orders.CountAsync(o => o.Status == OrderStatus.Pending),
                ConfirmedOrders = await _context.Orders.CountAsync(o => o.Status == OrderStatus.Confirmed),
                ProcessingOrders = await _context.Orders.CountAsync(o => o.Status == OrderStatus.Processing),
                ShippedOrders = await _context.Orders.CountAsync(o => o.Status == OrderStatus.Shipped),
                DeliveredOrders = await _context.Orders.CountAsync(o => o.Status == OrderStatus.Delivered),
                CancelledOrders = await _context.Orders.CountAsync(o => o.Status == OrderStatus.Cancelled),

                // آخر المنتجات المضافة
                RecentProducts = await _context.Products
                    .OrderByDescending(p => p.CreatedAt)
                    .Take(5)
                    .ToListAsync(),

                // آخر الطلبات
                RecentOrders = await _context.Orders
                    .Include(o => o.User)
                    .OrderByDescending(o => o.OrderDate)
                    .Take(5)
                    .ToListAsync(),

                // المنتجات الأكثر مبيعًا
                TopSellingProducts = await _context.OrderItems
                    .GroupBy(oi => oi.ProductId)
                    .Select(g => new TopSellingProductViewModel
                    {
                        ProductId = g.Key,
                        TotalQuantity = g.Sum(oi => oi.Quantity)
                    })
                    .OrderByDescending(p => p.TotalQuantity)
                    .Take(5)
                    .ToListAsync()
            };

            // الحصول على تفاصيل المنتجات الأكثر مبيعًا
            foreach (var product in dashboardViewModel.TopSellingProducts)
            {
                var productDetails = await _context.Products.FindAsync(product.ProductId);
                if (productDetails != null)
                {
                    product.ProductName = productDetails.Name;
                    product.ProductPrice = productDetails.Price;
                    product.ProductImageUrl = productDetails.ImageUrl;
                }
            }

            // إجمالي المبيعات
            dashboardViewModel.TotalSales = await _context.Orders
                .Where(o => o.Status == OrderStatus.Delivered)
                .SumAsync(o => o.TotalAmount);

            return View(dashboardViewModel);
        }

        // GET: Admin/Products
        public async Task<IActionResult> Products()
        {
            var products = await _context.Products
                .Include(p => p.Category)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();

            return View(products);
        }

        // GET: Admin/Categories
        public async Task<IActionResult> Categories()
        {
            var categories = await _context.Categories
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();

            return View(categories);
        }

        // GET: Admin/Orders
        public async Task<IActionResult> Orders()
        {
            var orders = await _context.Orders
                .Include(o => o.User)
                .OrderByDescending(o => o.OrderDate)
                .ToListAsync();

            return View(orders);
        }

        // GET: Admin/Users
        public async Task<IActionResult> Users()
        {
            var users = await _userManager.Users.ToListAsync();
            var userViewModels = new List<UserViewModel>();

            foreach (var user in users)
            {
                var roles = await _userManager.GetRolesAsync(user);
                var ordersCount = await _context.Orders.CountAsync(o => o.UserId == user.Id);

                userViewModels.Add(new UserViewModel
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList(),
                    OrdersCount = ordersCount
                });
            }

            return View(userViewModels);
        }

        // GET: Admin/CarouselImages
        public async Task<IActionResult> CarouselImages()
        {
            var carouselImages = await _context.CarouselImages
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();

            return View(carouselImages);
        }
    }
}
