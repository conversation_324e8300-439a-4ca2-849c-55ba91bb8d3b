using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using Abayat.Models;
using Abayat.Data;
using Microsoft.EntityFrameworkCore;

namespace Abayat.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly ApplicationDbContext _context;

        public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
        {
            _logger = logger;
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            // Get all categories with their products
            var categories = await _context.Categories
                .Include(c => c.Products)
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();

            // Get featured products (limited to 8)
            var featuredProducts = await _context.Products
                .Include(p => p.Category)
                .Where(p => p.IsAvailable)
                .OrderByDescending(p => p.CreatedAt)
                .Take(8)
                .ToListAsync();

            // الحصول على صور الشريط المتحرك من جدول CarouselImages فقط
            try
            {
                var carouselImages = await _context.CarouselImages
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.DisplayOrder)
                    .ToListAsync();

                // استخدام الصور الخارجية فقط للشريط المتحرك
                ViewBag.CarouselImages = carouselImages;
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، سجل الخطأ ولكن لا تستخدم صور المنتجات
                _logger.LogError(ex, "حدث خطأ أثناء جلب صور الشريط المتحرك");
                ViewBag.CarouselImages = new List<CarouselImage>();
            }

            // Get all products for display
            var allProducts = await _context.Products
                .Include(p => p.Category)
                .Where(p => p.IsAvailable)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();

            // Pass products to view
            ViewBag.FeaturedProducts = featuredProducts;
            ViewBag.AllProducts = allProducts;

            return View(categories);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        [Route("Home/PageNotFound")]
        public IActionResult PageNotFound(int? statusCode = null)
        {
            if (statusCode.HasValue)
            {
                // يمكن استخدام رمز الحالة لعرض رسائل خطأ مختلفة
                ViewBag.StatusCode = statusCode.Value;
                _logger.LogWarning("تم طلب صفحة غير موجودة. رمز الحالة: {StatusCode}", statusCode.Value);
            }

            return View("NotFound");
        }

        [Route("404")]
        public IActionResult NotFoundDirect()
        {
            _logger.LogWarning("تم طلب صفحة 404 مباشرة");
            ViewBag.StatusCode = 404;
            return View("NotFound");
        }

        public async Task<IActionResult> Carousel()
        {
            // الحصول على المنتجات المتاحة للعرض في الشريط المتحرك
            var carouselProducts = await _context.Products
                .Include(p => p.Category)
                .Where(p => p.IsAvailable && p.ShowInCarousel)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();

            return View(carouselProducts);
        }

        public IActionResult Splash()
        {
            return View();
        }
    }
}
