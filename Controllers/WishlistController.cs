using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Abayat.Models;
using Abayat.Data;
using Microsoft.EntityFrameworkCore;

namespace Abayat.Controllers
{
    public class WishlistController : Controller
    {
        private readonly ApplicationDbContext _context;

        public WishlistController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Wishlist
        public IActionResult Index()
        {
            // استرجاع قائمة المفضلة من cookie
            var wishlistJson = Request.Cookies["wishlist"];
            var wishlistItems = new List<int>();

            if (!string.IsNullOrEmpty(wishlistJson))
            {
                try
                {
                    wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                    Console.WriteLine($"تم استرجاع قائمة المفضلة بنجاح في Index: {wishlistJson}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المفضلة في Index: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمفضلة في Index");
            }

            // الحصول على المنتجات المفضلة من قاعدة البيانات
            var products = new List<Product>();
            if (wishlistItems.Any())
            {
                products = _context.Products
                    .Where(p => wishlistItems.Contains(p.Id))
                    .ToList();
            }

            // إذا كان هناك تناقض بين عدد العناصر في القائمة وعدد المنتجات المسترجعة، قم بتحديث ملف تعريف الارتباط
            if (wishlistItems.Count != products.Count)
            {
                Console.WriteLine($"تم اكتشاف تناقض في عدد المنتجات المفضلة. العدد في الكوكي: {wishlistItems.Count}, العدد المسترجع: {products.Count}");

                // تحديث قائمة المفضلة لتحتوي فقط على المنتجات الموجودة
                wishlistItems = products.Select(p => p.Id).ToList();

                // حفظ قائمة المفضلة المحدثة في cookie
                var cookieOptions = new CookieOptions
                {
                    Expires = DateTime.Now.AddDays(30),
                    HttpOnly = false, // السماح بالوصول من JavaScript
                    SameSite = SameSiteMode.Lax,
                    Secure = Request.IsHttps,
                    Path = "/"
                };

                Response.Cookies.Append("wishlist", System.Text.Json.JsonSerializer.Serialize(wishlistItems), cookieOptions);
            }

            return View(products);
        }

        // POST: Wishlist/FixWishlistCounter
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult FixWishlistCounter(int actualCount)
        {
            try
            {
                // استرجاع قائمة المفضلة من cookie
                var wishlistJson = Request.Cookies["wishlist"];
                var wishlistItems = new List<int>();

                if (!string.IsNullOrEmpty(wishlistJson))
                {
                    try
                    {
                        wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                        Console.WriteLine($"تم استرجاع قائمة المفضلة بنجاح في FixWishlistCounter: {wishlistJson}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ في تحليل قائمة المفضلة في FixWishlistCounter: {ex.Message}");
                    }
                }

                // التحقق من وجود تناقض بين العداد الفعلي وعدد العناصر في القائمة
                if (wishlistItems.Count != actualCount)
                {
                    Console.WriteLine($"تم اكتشاف تناقض في عداد المفضلة. العدد في الكوكي: {wishlistItems.Count}, العدد الفعلي: {actualCount}");

                    // إذا كانت القائمة فارغة ولكن العداد يشير إلى وجود عناصر، قم بإعادة تعيين الكوكي
                    if (wishlistItems.Count == 0 && actualCount > 0)
                    {
                        // في هذه الحالة، نحتاج إلى استرجاع المنتجات الفعلية من قاعدة البيانات
                        // ولكن بما أننا لا نعرف أي المنتجات كانت في المفضلة، سنقوم بإعادة تعيين الكوكي فقط
                        Console.WriteLine("القائمة فارغة ولكن العداد يشير إلى وجود عناصر. إعادة تعيين الكوكي.");
                    }
                    // إذا كان العداد يشير إلى عدم وجود عناصر ولكن القائمة تحتوي على عناصر، قم بتحديث العداد
                    else if (wishlistItems.Count > 0 && actualCount == 0)
                    {
                        Console.WriteLine("القائمة تحتوي على عناصر ولكن العداد يشير إلى عدم وجود عناصر. تحديث العداد.");
                    }

                    // حفظ قائمة المفضلة المحدثة في cookie
                    var cookieOptions = new CookieOptions
                    {
                        Expires = DateTime.Now.AddDays(30),
                        HttpOnly = false, // السماح بالوصول من JavaScript
                        SameSite = SameSiteMode.Lax,
                        Secure = Request.IsHttps,
                        Path = "/"
                    };

                    Response.Cookies.Append("wishlist", System.Text.Json.JsonSerializer.Serialize(wishlistItems), cookieOptions);

                    return Json(new { success = true, message = "تم إصلاح عداد المفضلة بنجاح", count = wishlistItems.Count });
                }

                return Json(new { success = true, message = "لا يوجد تناقض في عداد المفضلة", count = wishlistItems.Count });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إصلاح عداد المفضلة: {ex.Message}");
                return Json(new { success = false, message = "حدث خطأ أثناء إصلاح عداد المفضلة" });
            }
        }

        // GET: Wishlist/GetWishlistItemsCount
        [HttpGet]
        public IActionResult GetWishlistItemsCount()
        {
            Console.WriteLine("🔍 تم استدعاء GetWishlistItemsCount من الخادم");
            Console.WriteLine($"📍 User-Agent: {Request.Headers["User-Agent"]}");
            Console.WriteLine($"📍 Referer: {Request.Headers["Referer"]}");

            // استرجاع قائمة المفضلة من cookie
            var wishlistJson = Request.Cookies["wishlist"];
            var wishlistItems = new List<int>();

            if (!string.IsNullOrEmpty(wishlistJson))
            {
                try
                {
                    wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                    Console.WriteLine($"تم استرجاع قائمة المفضلة بنجاح في GetWishlistItemsCount: {wishlistJson}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المفضلة في GetWishlistItemsCount: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمفضلة في GetWishlistItemsCount");
            }

            return Json(new { count = wishlistItems?.Count ?? 0 });
        }

        // GET: Wishlist/GetWishlistItems
        [HttpGet]
        public IActionResult GetWishlistItems()
        {
            // استرجاع قائمة المفضلة من cookie
            var wishlistJson = Request.Cookies["wishlist"];
            var wishlistItems = new List<int>();

            if (!string.IsNullOrEmpty(wishlistJson))
            {
                try
                {
                    wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                    Console.WriteLine($"تم استرجاع قائمة المفضلة بنجاح في GetWishlistItems: {wishlistJson}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المفضلة في GetWishlistItems: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمفضلة في GetWishlistItems");
            }

            return Json(new { items = wishlistItems, count = wishlistItems?.Count ?? 0 });
        }

        // POST: Wishlist/AddToWishlist
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult AddToWishlist(int productId)
        {
            // التحقق من توفر المنتج قبل إضافته للمفضلة
            var product = _context.Products.FirstOrDefault(p => p.Id == productId);

            if (product == null)
            {
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "المنتج غير موجود" });
                }

                TempData["ErrorMessage"] = "المنتج غير موجود";
                string refUrl = Request.Headers["Referer"].ToString();
                return Redirect(!string.IsNullOrEmpty(refUrl) ? refUrl : "/Products");
            }

            // استرجاع قائمة المفضلة من cookie
            var wishlistJson = Request.Cookies["wishlist"];
            var wishlistItems = new List<int>();

            if (!string.IsNullOrEmpty(wishlistJson))
            {
                try
                {
                    wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                    Console.WriteLine($"تم استرجاع قائمة المفضلة بنجاح: {wishlistJson}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المفضلة: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمفضلة");
            }

            // التحقق مما إذا كان المنتج موجودًا بالفعل في المفضلة
            bool isInWishlist = wishlistItems?.Contains(productId) ?? false;
            bool success = false;
            string message = "";

            if (isInWishlist)
            {
                // إزالة المنتج من المفضلة
                wishlistItems.Remove(productId);
                message = "تمت إزالة المنتج من المفضلة";
                success = true;
            }
            else
            {
                // إضافة المنتج إلى المفضلة
                wishlistItems ??= new List<int>();
                wishlistItems.Add(productId);
                message = "تمت إضافة المنتج إلى المفضلة";
                success = true;
            }

            // حفظ قائمة المفضلة المحدثة في cookie
            var cookieOptions = new CookieOptions
            {
                Expires = DateTime.Now.AddDays(30),
                HttpOnly = false, // السماح بالوصول من JavaScript
                SameSite = SameSiteMode.Lax,
                Secure = Request.IsHttps,
                Path = "/"
            };

            Response.Cookies.Append("wishlist", System.Text.Json.JsonSerializer.Serialize(wishlistItems), cookieOptions);

            // إرجاع استجابة JSON للطلبات AJAX
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new {
                    success = success,
                    message = message,
                    isInWishlist = isInWishlist, // الحالة الجديدة الصحيحة
                    count = wishlistItems?.Count ?? 0, // التأكد من أن العدد ليس null
                    items = wishlistItems // إضافة قائمة العناصر للتحقق من التناقض
                });
            }

            // إرجاع إلى الصفحة السابقة للطلبات العادية
            TempData[success ? "SuccessMessage" : "ErrorMessage"] = message;
            string backUrl = Request.Headers["Referer"].ToString();
            return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/Products");
        }
    }
}
