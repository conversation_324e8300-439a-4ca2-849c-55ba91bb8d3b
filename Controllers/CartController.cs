using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Abayat.Services;
using Abayat.Models;
using System.Threading.Tasks;
using System.Security.Claims;
using System;
using System.Collections.Generic;
using System.Linq;
using Abayat.Data;
using Microsoft.EntityFrameworkCore;
using Abayat.ViewModels;

namespace Abayat.Controllers
{
    public class CartController : Controller
    {
        private readonly IShoppingCartService _cartService;
        private readonly ApplicationDbContext _context;

        public CartController(IShoppingCartService cartService, ApplicationDbContext context)
        {
            _cartService = cartService;
            _context = context;
        }

        // GET: Cart
        public async Task<IActionResult> Index()
        {
            if (!User.Identity.IsAuthenticated)
            {
                // للزوار غير المسجلين، الحصول على السلة من الجلسة
                var guestCartItems = HttpContext.Session.Get<List<CartItem>>("GuestCart") ?? new List<CartItem>();

                // إنشاء سلة مؤقتة للعرض
                var guestCart = new ShoppingCart
                {
                    CartItems = new List<CartItem>()
                };

                // تحميل معلومات المنتجات لعناصر السلة
                foreach (var item in guestCartItems)
                {
                    // الحصول على معلومات المنتج من قاعدة البيانات
                    var product = await _context.Products
                        .Include(p => p.Category)
                        .FirstOrDefaultAsync(p => p.Id == item.ProductId);

                    if (product != null)
                    {
                        // إنشاء نسخة من عنصر السلة مع معلومات المنتج
                        var cartItem = new CartItem
                        {
                            Id = item.Id,
                            ProductId = item.ProductId,
                            Product = product,
                            Quantity = item.Quantity,
                            CreatedAt = item.CreatedAt
                        };

                        guestCart.CartItems.Add(cartItem);
                    }
                }

                return View(guestCart);
            }
            else
            {
                // للمستخدمين المسجلين، استخدام الخدمة العادية
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var cart = await _cartService.GetCartWithItemsAsync(userId);
                return View(cart);
            }
        }

        // POST: Cart/AddToCart
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddToCart(int productId, int quantity = 1)
        {
            if (quantity <= 0)
            {
                quantity = 1;
            }

            // التحقق من توفر المنتج قبل إضافته للسلة
            var product = await _context.Products.FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null || !product.IsAvailable)
            {
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "المنتج غير متوفر حالياً" });
                }

                TempData["ErrorMessage"] = "المنتج غير متوفر حالياً";
                return Redirect(Request.Headers["Referer"].ToString() ?? "/Products");
            }

            // التحقق مما إذا كان المستخدم مسجل الدخول
            if (!User.Identity.IsAuthenticated)
            {
                // للزوار غير المسجلين، حفظ المنتج في الجلسة
                var cartItems = HttpContext.Session.Get<List<CartItem>>("GuestCart") ?? new List<CartItem>();

                // التحقق مما إذا كان المنتج موجوداً بالفعل في السلة
                var existingItem = cartItems.FirstOrDefault(i => i.ProductId == productId);
                if (existingItem != null)
                {
                    existingItem.Quantity += quantity;
                }
                else
                {
                    // إضافة منتج جديد
                    cartItems.Add(new CartItem
                    {
                        ProductId = productId,
                        Quantity = quantity,
                        CreatedAt = DateTime.Now
                    });
                }

                // حفظ السلة المحدثة في الجلسة
                HttpContext.Session.Set("GuestCart", cartItems);
                TempData["SuccessMessage"] = "تمت إضافة المنتج إلى السلة بنجاح";

                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = true, message = "تمت إضافة المنتج إلى السلة بنجاح" });
                }
            }
            else
            {
                // للمستخدمين المسجلين، استخدام الخدمة العادية
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                try
                {
                    await _cartService.AddItemToCartAsync(userId, productId, quantity);
                    TempData["SuccessMessage"] = "تمت إضافة المنتج إلى السلة بنجاح";

                    // إضافة رد JSON لتحديث السلة
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        return Json(new { success = true, message = "تمت إضافة المنتج إلى السلة بنجاح" });
                    }
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = ex.Message;

                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        return Json(new { success = false, message = ex.Message });
                    }
                }
            }

            // العودة إلى الصفحة السابقة
            return Redirect(Request.Headers["Referer"].ToString() ?? "/Products");
        }

        // POST: Cart/UpdateQuantity
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateQuantity(int cartItemId, int quantity, int productId)
        {
            if (!User.Identity.IsAuthenticated)
            {
                // للزوار غير المسجلين، تحديث السلة في الجلسة
                var cartItems = HttpContext.Session.Get<List<CartItem>>("GuestCart") ?? new List<CartItem>();
                var cartItem = cartItems.FirstOrDefault(i => i.ProductId == productId);

                if (cartItem != null)
                {
                    if (quantity <= 0)
                    {
                        // إزالة العنصر من السلة
                        cartItems.Remove(cartItem);
                        TempData["SuccessMessage"] = "تم حذف المنتج من السلة";
                    }
                    else
                    {
                        // تحديث الكمية
                        cartItem.Quantity = quantity;
                        TempData["SuccessMessage"] = "تم تحديث الكمية بنجاح";
                    }

                    // حفظ السلة المحدثة في الجلسة
                    HttpContext.Session.Set("GuestCart", cartItems);
                }
            }
            else
            {
                // للمستخدمين المسجلين، استخدام الخدمة العادية
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                try
                {
                    if (quantity <= 0)
                    {
                        await _cartService.RemoveCartItemAsync(userId, cartItemId);
                        TempData["SuccessMessage"] = "تم حذف المنتج من السلة";
                    }
                    else
                    {
                        await _cartService.UpdateCartItemAsync(userId, cartItemId, quantity);
                        TempData["SuccessMessage"] = "تم تحديث الكمية بنجاح";
                    }
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = ex.Message;
                }
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Cart/RemoveItem
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RemoveItem(int cartItemId, int productId)
        {
            if (!User.Identity.IsAuthenticated)
            {
                // للزوار غير المسجلين، إزالة العنصر من السلة في الجلسة
                var cartItems = HttpContext.Session.Get<List<CartItem>>("GuestCart") ?? new List<CartItem>();
                var cartItem = cartItems.FirstOrDefault(i => i.ProductId == productId);

                if (cartItem != null)
                {
                    cartItems.Remove(cartItem);
                    HttpContext.Session.Set("GuestCart", cartItems);
                    TempData["SuccessMessage"] = "تم حذف المنتج من السلة";
                }
            }
            else
            {
                // للمستخدمين المسجلين، استخدام الخدمة العادية
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                try
                {
                    await _cartService.RemoveCartItemAsync(userId, cartItemId);
                    TempData["SuccessMessage"] = "تم حذف المنتج من السلة";
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = ex.Message;
                }
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Cart/ClearCart
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ClearCart()
        {
            if (!User.Identity.IsAuthenticated)
            {
                // للزوار غير المسجلين، تفريغ السلة في الجلسة
                HttpContext.Session.Set("GuestCart", new List<CartItem>());
                TempData["SuccessMessage"] = "تم تفريغ السلة بنجاح";
            }
            else
            {
                // للمستخدمين المسجلين، استخدام الخدمة العادية
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                try
                {
                    await _cartService.ClearCartAsync(userId);
                    TempData["SuccessMessage"] = "تم تفريغ السلة بنجاح";
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = ex.Message;
                }
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Cart/Checkout
        public async Task<IActionResult> Checkout()
        {
            // التحقق من وجود منتجات في السلة
            if (!User.Identity.IsAuthenticated)
            {
                // للزوار غير المسجلين، الحصول على السلة من الجلسة
                var guestCartItems = HttpContext.Session.Get<List<CartItem>>("GuestCart") ?? new List<CartItem>();

                if (guestCartItems.Count == 0)
                {
                    TempData["ErrorMessage"] = "لا يمكن إتمام الطلب، السلة فارغة";
                    return RedirectToAction(nameof(Index));
                }
            }
            else
            {
                // للمستخدمين المسجلين، التحقق من السلة
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var cart = await _cartService.GetCartWithItemsAsync(userId);

                if (cart.CartItems == null || cart.CartItems.Count == 0)
                {
                    TempData["ErrorMessage"] = "لا يمكن إتمام الطلب، السلة فارغة";
                    return RedirectToAction(nameof(Index));
                }
            }

            // إذا كان المستخدم مسجل، نقوم بملء بعض البيانات تلقائيًا
            if (User.Identity.IsAuthenticated)
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == User.FindFirstValue(ClaimTypes.NameIdentifier));
                if (user != null)
                {
                    var model = new CheckoutViewModel
                    {
                        FullName = user.Name,
                        PhoneNumber = user.PhoneNumber ?? ""
                    };
                    return View("ValidatedCheckout", model);
                }
            }

            return View("ValidatedCheckout");
        }

        // AJAX: Cart/GetCartItemsCount
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetCartItemsCount()
        {
            if (!User.Identity.IsAuthenticated)
            {
                // للزوار غير المسجلين، الحصول على السلة من الجلسة
                var guestCartItems = HttpContext.Session.Get<List<CartItem>>("GuestCart") ?? new List<CartItem>();
                int guestCount = guestCartItems.Sum(item => item.Quantity);
                return Json(new { count = guestCount });
            }

            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userCount = await _cartService.GetCartItemsCountAsync(userId);
            return Json(new { count = userCount });
        }
    }
}
