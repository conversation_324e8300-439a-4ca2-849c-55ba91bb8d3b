using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Abayat.Models;
using Abayat.Data;
using Microsoft.EntityFrameworkCore;

namespace Abayat.Controllers
{
    public class CompareController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CompareController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Compare/GetCompareItemsCount
        [HttpGet]
        public IActionResult GetCompareItemsCount()
        {
            // استرجاع قائمة المقارنة من cookie
            var compareJson = Request.Cookies["compareList"];
            var compareItems = new List<int>();

            if (!string.IsNullOrEmpty(compareJson))
            {
                try
                {
                    compareItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(compareJson);
                    Console.WriteLine($"تم استرجاع قائمة المقارنة بنجاح في GetCompareItemsCount: {compareJson}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المقارنة في GetCompareItemsCount: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمقارنة في GetCompareItemsCount");
            }

            return Json(new { count = compareItems?.Count ?? 0 });
        }

        // GET: Compare/GetCompareItems
        [HttpGet]
        public IActionResult GetCompareItems()
        {
            // استرجاع قائمة المقارنة من cookie
            var compareJson = Request.Cookies["compareList"];
            var compareItems = new List<int>();

            if (!string.IsNullOrEmpty(compareJson))
            {
                try
                {
                    compareItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(compareJson);
                    Console.WriteLine($"تم استرجاع قائمة المقارنة بنجاح في GetCompareItems: {compareJson}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المقارنة في GetCompareItems: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمقارنة في GetCompareItems");
            }

            return Json(new { items = compareItems });
        }

        // POST: Compare/AddToCompare
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult AddToCompare(int productId)
        {
            // التحقق من توفر المنتج قبل إضافته للمقارنة
            var product = _context.Products.FirstOrDefault(p => p.Id == productId);

            if (product == null)
            {
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "المنتج غير موجود" });
                }

                TempData["ErrorMessage"] = "المنتج غير موجود";
                string refUrl = Request.Headers["Referer"].ToString();
                return Redirect(!string.IsNullOrEmpty(refUrl) ? refUrl : "/Products");
            }

            // استرجاع قائمة المقارنة من cookie
            var compareJson = Request.Cookies["compareList"];
            var compareItems = new List<int>();

            if (!string.IsNullOrEmpty(compareJson))
            {
                try
                {
                    compareItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(compareJson);
                    Console.WriteLine($"تم استرجاع قائمة المقارنة بنجاح: {compareJson}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المقارنة: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمقارنة");
            }

            // التحقق مما إذا كان المنتج موجودًا بالفعل في المقارنة
            bool isInCompareList = compareItems?.Contains(productId) ?? false;
            bool success = false;
            string message = "";
            int removedId = -1;

            if (isInCompareList)
            {
                // إزالة المنتج من المقارنة
                compareItems.Remove(productId);
                message = "تمت إزالة المنتج من المقارنة";
                success = true;
            }
            else
            {
                // إضافة المنتج إلى المقارنة
                compareItems ??= new List<int>();

                // تحديد عدد المنتجات في المقارنة (4 كحد أقصى)
                if (compareItems.Count >= 4)
                {
                    // إزالة أول منتج من القائمة
                    removedId = compareItems[0];
                    compareItems.RemoveAt(0);
                    message = "تمت إضافة المنتج إلى المقارنة (تم إزالة منتج آخر لتجاوز الحد الأقصى)";
                }
                else
                {
                    message = "تمت إضافة المنتج إلى المقارنة";
                }

                compareItems.Add(productId);
                success = true;
            }

            // حفظ قائمة المقارنة المحدثة في cookie
            var cookieOptions = new CookieOptions
            {
                Expires = DateTime.Now.AddDays(30),
                HttpOnly = false, // السماح بالوصول من JavaScript
                SameSite = SameSiteMode.Lax,
                Secure = Request.IsHttps,
                Path = "/"
            };

            Response.Cookies.Append("compareList", System.Text.Json.JsonSerializer.Serialize(compareItems), cookieOptions);

            // إرجاع استجابة JSON للطلبات AJAX
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new {
                    success = success,
                    message = message,
                    isInCompareList = !isInCompareList, // عكس الحالة السابقة
                    count = compareItems.Count,
                    removedId = removedId
                });
            }

            // إرجاع إلى الصفحة السابقة للطلبات العادية
            TempData[success ? "SuccessMessage" : "ErrorMessage"] = message;
            string backUrl = Request.Headers["Referer"].ToString();
            return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/Products");
        }
    }
}
