using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Abayat.Services;
using Abayat.Models;
using Abayat.ViewModels;
using System.Threading.Tasks;
using System.Security.Claims;
using System;
using System.Collections.Generic;

namespace Abayat.Controllers
{
    public class OrdersController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly IShoppingCartService _cartService;

        public OrdersController(IOrderService orderService, IShoppingCartService cartService)
        {
            _orderService = orderService;
            _cartService = cartService;
        }

        // GET: Orders
        [Authorize(Roles = "Customer")]
        public async Task<IActionResult> Index(string status)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var orders = await _orderService.GetUserOrdersAsync(userId);

            // تطبيق التصفية حسب الحالة إذا تم تحديدها
            if (!string.IsNullOrEmpty(status))
            {
                ViewData["StatusFilter"] = status;
            }

            return View(orders);
        }

        // GET: Orders/Details/5
        [Authorize(Roles = "Customer,Admin")]
        public async Task<IActionResult> Details(int id)
        {
            Order order;

            if (User.IsInRole("Admin"))
            {
                // المدير يمكنه عرض أي طلب
                order = await _orderService.GetOrderByIdAsync(id);
            }
            else
            {
                // العميل يمكنه عرض طلباته فقط
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                order = await _orderService.GetOrderByIdAsync(id, userId);
            }

            if (order == null)
            {
                return NotFound();
            }

            return View(order);
        }

        // POST: Orders/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CheckoutViewModel model)
        {
            try
            {
                // تسجيل بداية عملية إنشاء الطلب
                Console.WriteLine("بدء عملية إنشاء الطلب في OrdersController.Create");

                if (!ModelState.IsValid)
                {
                    Console.WriteLine("نموذج البيانات غير صالح");
                    Console.WriteLine("أخطاء التحقق من صحة النموذج:");

                    foreach (var state in ModelState)
                    {
                        foreach (var error in state.Value.Errors)
                        {
                            Console.WriteLine($"- {state.Key}: {error.ErrorMessage}");
                        }
                    }

                    // إذا كان الطلب من AJAX، أرجع رسالة خطأ مع تفاصيل الأخطاء
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        var errors = ModelState.ToDictionary(
                            kvp => kvp.Key,
                            kvp => kvp.Value.Errors.Select(e => e.ErrorMessage).ToArray()
                        );

                        return Json(new {
                            success = false,
                            message = "يرجى التحقق من صحة البيانات المدخلة",
                            errors = errors
                        });
                    }

                    return View("~/Views/Cart/ValidatedCheckout.cshtml", model);
                }

                // التحقق مما إذا كان المستخدم مسجل الدخول
                if (User.Identity != null && User.Identity.IsAuthenticated)
                {
                    // للمستخدمين المسجلين
                    var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                    Console.WriteLine($"المستخدم مسجل دخول. معرف المستخدم: {userId}");

                    if (string.IsNullOrEmpty(userId))
                    {
                        Console.WriteLine("خطأ: معرف المستخدم فارغ");
                        TempData["ErrorMessage"] = "خطأ في معرف المستخدم";
                        return View("~/Views/Cart/ValidatedCheckout.cshtml", model);
                    }

                    var cart = await _cartService.GetCartWithItemsAsync(userId);
                    Console.WriteLine($"تم استرجاع سلة المستخدم. عدد العناصر: {cart.CartItems?.Count ?? 0}");

                    if (cart.CartItems == null || cart.CartItems.Count == 0)
                    {
                        Console.WriteLine("خطأ: سلة المستخدم فارغة");
                        TempData["ErrorMessage"] = "لا يمكن إتمام الطلب، السلة فارغة";
                        return RedirectToAction("Index", "Cart");
                    }

                    try
                    {
                        Console.WriteLine("جاري إنشاء الطلب للمستخدم المسجل...");
                        var order = await _orderService.CreateOrderFromCartAsync(
                            userId,
                            model.FullName,
                            model.PhoneNumber,
                            model.Address,
                            model.Notes ?? string.Empty
                        );

                        Console.WriteLine($"تم إنشاء الطلب بنجاح. رقم الطلب: {order.Id}");
                        TempData["SuccessMessage"] = "تم إنشاء الطلب بنجاح. رقم الطلب: " + order.Id;

                        // إذا كان الطلب من AJAX، أرجع رسالة نجاح
                        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                        {
                            return Json(new { success = true, message = "تم إنشاء الطلب بنجاح. رقم الطلب: " + order.Id, orderId = order.Id });
                        }

                        return RedirectToAction("OrderSuccess", new { id = order.Id });
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ أثناء إنشاء الطلب للمستخدم المسجل: {ex.Message}");
                        Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                        TempData["ErrorMessage"] = ex.Message;

                        // إذا كان الطلب من AJAX، أرجع رسالة خطأ
                        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                        {
                            return Json(new { success = false, message = ex.Message });
                        }

                        return View("~/Views/Cart/ValidatedCheckout.cshtml", model);
                    }
                }
                else
                {
                    Console.WriteLine("المستخدم غير مسجل دخول. جاري إنشاء طلب للزائر...");

                    // للزوار غير المسجلين
                    var guestCartItems = HttpContext.Session.Get<List<CartItem>>("GuestCart") ?? new List<CartItem>();
                    Console.WriteLine($"تم استرجاع سلة الزائر. عدد العناصر: {guestCartItems.Count}");

                    if (guestCartItems.Count == 0)
                    {
                        Console.WriteLine("خطأ: سلة الزائر فارغة");
                        TempData["ErrorMessage"] = "لا يمكن إتمام الطلب، السلة فارغة";
                        return RedirectToAction("Index", "Cart");
                    }

                    try
                    {
                        // إنشاء طلب للزائر
                        Console.WriteLine("جاري إنشاء الطلب للزائر...");
                        var order = await _orderService.CreateGuestOrderAsync(
                            guestCartItems,
                            model.FullName,
                            model.PhoneNumber,
                            model.Address,
                            model.Notes ?? string.Empty
                        );

                        // تفريغ سلة الزائر بعد إنشاء الطلب
                        HttpContext.Session.Set("GuestCart", new List<CartItem>());

                        Console.WriteLine($"تم إنشاء طلب الزائر بنجاح. رقم الطلب: {order.Id}");
                        TempData["SuccessMessage"] = "تم إنشاء الطلب بنجاح. رقم الطلب: " + order.Id;

                        // إذا كان الطلب من AJAX، أرجع رسالة نجاح
                        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                        {
                            return Json(new { success = true, message = "تم إنشاء الطلب بنجاح. رقم الطلب: " + order.Id, orderId = order.Id });
                        }

                        return RedirectToAction("OrderSuccess", new { id = order.Id });
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ أثناء إنشاء الطلب للزائر: {ex.Message}");
                        Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                        TempData["ErrorMessage"] = ex.Message;

                        // إذا كان الطلب من AJAX، أرجع رسالة خطأ
                        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                        {
                            return Json(new { success = false, message = ex.Message });
                        }

                        return View("~/Views/Cart/ValidatedCheckout.cshtml", model);
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ عام أثناء إنشاء الطلب: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");

                TempData["ErrorMessage"] = "حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.";

                // إذا كان الطلب من AJAX، أرجع رسالة خطأ
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى." });
                }

                return View("~/Views/Cart/ValidatedCheckout.cshtml", model);
            }
        }

        // GET: Orders/OrderSuccess
        public async Task<IActionResult> OrderSuccess(int id)
        {
            // التحقق من وجود الطلب في قاعدة البيانات
            var order = await _orderService.GetOrderByIdAsync(id);

            if (order == null)
            {
                TempData["ErrorMessage"] = "الطلب غير موجود";
                return RedirectToAction("Index", "Home");
            }

            ViewData["OrderId"] = id;
            return View();
        }

        // POST: Orders/Cancel/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Customer")]
        public async Task<IActionResult> Cancel(int id)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            try
            {
                await _orderService.CancelOrderAsync(id, userId);
                TempData["SuccessMessage"] = "تم إلغاء الطلب بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = ex.Message;
            }

            return RedirectToAction(nameof(Details), new { id });
        }

        // GET: Orders/Manage (للمدير فقط)
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Manage(string userId = null)
        {
            // تسجيل بداية عملية استرجاع الطلبات
            Console.WriteLine("بدء عملية استرجاع الطلبات في OrdersController.Manage");

            try
            {
                var orders = await _orderService.GetAllOrdersAsync();
                Console.WriteLine($"تم استرجاع {orders.Count} طلب في OrdersController.Manage");

                // إذا تم تحديد معرف المستخدم، قم بتصفية الطلبات لهذا المستخدم فقط
                if (!string.IsNullOrEmpty(userId))
                {
                    orders = orders.Where(o => o.UserId == userId).ToList();
                    ViewData["FilteredUserId"] = userId;

                    // الحصول على اسم المستخدم لعرضه في الصفحة
                    if (orders.Any() && orders.First().User != null)
                    {
                        ViewData["FilteredUserName"] = orders.First().User.Name;
                    }
                }

                // التحقق من وجود طلبات قبل إرجاع النتيجة
                if (orders.Count == 0)
                {
                    Console.WriteLine("لا توجد طلبات للعرض في OrdersController.Manage");
                    ViewData["NoOrders"] = "لا توجد طلبات للعرض";
                }
                else
                {
                    Console.WriteLine("تم العثور على طلبات وسيتم عرضها في OrdersController.Manage");
                }

                return View(orders);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"حدث خطأ أثناء استرجاع الطلبات: {ex.Message}");
                TempData["ErrorMessage"] = "حدث خطأ أثناء استرجاع الطلبات: " + ex.Message;
                return View(new List<Order>());
            }
        }

        // POST: Orders/UpdateStatus/5 (للمدير فقط)
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateStatus(int id, OrderStatus status)
        {
            try
            {
                await _orderService.UpdateOrderStatusAsync(id, status);

                // إذا كان الطلب من AJAX، أرجع استجابة JSON
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = true, message = "تم تحديث حالة الطلب بنجاح" });
                }

                TempData["SuccessMessage"] = "تم تحديث حالة الطلب بنجاح";
                return RedirectToAction(nameof(Manage));
            }
            catch (Exception ex)
            {
                // إذا كان الطلب من AJAX، أرجع استجابة JSON
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = ex.Message });
                }

                TempData["ErrorMessage"] = ex.Message;
                return RedirectToAction(nameof(Manage));
            }
        }
    }
}
